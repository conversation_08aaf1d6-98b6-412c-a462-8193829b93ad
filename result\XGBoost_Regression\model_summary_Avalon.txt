=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: Avalon
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 2667.85 seconds

Best Parameters:
  alpha: 0.1
  colsample_bytree: 1.0
  gamma: 0
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.2480
   RMSE: 0.4980
   MAE: 0.3455
   R2: 0.8596
   MAPE: 1.0290

2. 10-Fold Cross Validation Results:
   Mean R²: 0.3362 ± 0.1486
   Best CV Score: 0.3362
   Individual fold R² scores: ['0.5581', '0.6325', '0.3741', '0.2834', '0.3592', '0.2371', '0.1427', '0.1641', '0.3197', '0.2915']

3. External Validation Set Metrics:
   MSE: 0.9885
   RMSE: 0.9942
   MAE: 0.7491
   R2: 0.4499
   MAPE: 2.9102

Feature Information:
  Numerical Features: []
  Categorical Features: []

