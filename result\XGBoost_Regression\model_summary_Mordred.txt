=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: Mordred
Target Variable: pEC50_new

Best Parameters:
  alpha: 0
  colsample_bytree: 0.8
  gamma: 0.1
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 1.0

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.0593
   RMSE: 0.2435
   MAE: 0.1699
   R2: 0.9664
   MAPE: 0.4607

2. 10-Fold Cross Validation Results:
   Mean R²: 0.4266 ± 0.1255
   Best CV Score: 0.4266
   Individual fold R² scores: ['0.5301', '0.6233', '0.5670', '0.5007', '0.4038', '0.2388', '0.2921', '0.2562', '0.4300', '0.4246']

3. External Validation Set Metrics:
   MSE: 0.7618
   RMSE: 0.8728
   MAE: 0.6444
   R2: 0.5761
   MAPE: 1.6826

Feature Information:
  Numerical Features: []
  Categorical Features: []

