import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from sklearn.model_selection import train_test_split
import numpy as np

class SMILESDataset(Dataset):
    def __init__(self, smiles_list, tokenizer, max_length=512, labels=None):
        self.smiles_list = smiles_list
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.labels = labels
        
    def __len__(self):
        return len(self.smiles_list)
    
    def __getitem__(self, idx):
        smiles = str(self.smiles_list[idx])
        
        encoding = self.tokenizer(
            smiles,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        item = {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten()
        }
        
        if self.labels is not None:
            item['labels'] = torch.tensor(self.labels[idx], dtype=torch.float)
            
        return item

class DataProcessor:
    def __init__(self, config):
        self.config = config
        self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)

    def load_pretrain_data(self):
        """加载预训练数据"""
        df = pd.read_csv(self.config.pretrain_data)
        smiles_list = df['smiles'].tolist()
        return smiles_list

    def load_finetune_data(self):
        """加载微调数据"""
        df = pd.read_csv(self.config.finetune_data)
        smiles_list = df['smiles'].tolist()
        labels = df[self.config.target_column].values
        return smiles_list, labels
    
    def create_pretrain_dataloader(self):
        """创建预训练数据加载器"""
        smiles_list = self.load_pretrain_data()
        dataset = SMILESDataset(smiles_list, self.tokenizer, self.config.max_length)

        dataloader = DataLoader(
            dataset,
            batch_size=self.config.pretrain_batch_size,
            shuffle=True,
            num_workers=0
        )
        return dataloader
    
    def create_finetune_dataloaders(self, test_size=0.2, val_size=0.1):
        """创建微调数据加载器"""
        smiles_list, labels = self.load_finetune_data()
        
        # 分割数据
        X_temp, X_test, y_temp, y_test = train_test_split(
            smiles_list, labels, test_size=test_size, random_state=self.config.seed
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_size/(1-test_size), random_state=self.config.seed
        )

        # 创建数据集
        train_dataset = SMILESDataset(X_train, self.tokenizer, self.config.max_length, y_train)
        val_dataset = SMILESDataset(X_val, self.tokenizer, self.config.max_length, y_val)
        test_dataset = SMILESDataset(X_test, self.tokenizer, self.config.max_length, y_test)

        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=self.config.finetune_batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.config.finetune_batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=self.config.finetune_batch_size, shuffle=False)
        
        return train_loader, val_loader, test_loader
