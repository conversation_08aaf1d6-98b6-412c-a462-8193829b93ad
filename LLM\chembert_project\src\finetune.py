import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer, get_linear_schedule_with_warmup
from torch.optim import Adam<PERSON>
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from tqdm import tqdm
import os
import numpy as np

class RegressionModel(nn.Module):
    def __init__(self, model_name, num_labels=1):
        super().__init__()
        self.bert = AutoModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(0.1)
        self.regressor = nn.Linear(self.bert.config.hidden_size, num_labels)
        
    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        pooled_output = self.dropout(pooled_output)
        logits = self.regressor(pooled_output)
        return logits

class FineTuner:
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.device)

        # 检查是否有预训练模型
        if os.path.exists(config.pretrain_output_dir):
            model_path = config.pretrain_output_dir
            print(f"使用预训练模型: {model_path}")
        else:
            model_path = config.model_name
            print(f"使用原始模型: {model_path}")

        self.model = RegressionModel(model_path)
        self.model.to(self.device)
        
    def train(self, train_loader, val_loader):
        """执行微调训练"""
        os.makedirs(self.config.finetune_output_dir, exist_ok=True)

        # 显示微调参数
        print("微调参数:")
        print(f"  学习率: {self.config.finetune_lr}")
        print(f"  批次大小: {self.config.finetune_batch_size}")
        print(f"  训练轮数: {self.config.finetune_epochs}")
        print(f"  预热步数: {self.config.warmup_steps}")
        print(f"  训练批次数: {len(train_loader)}")
        print(f"  验证批次数: {len(val_loader)}")
        print(f"  设备: {self.device}")
        print(f"  输出目录: {self.config.finetune_output_dir}")
        print("-" * 50)

        # 优化器和调度器
        optimizer = AdamW(self.model.parameters(), lr=self.config.finetune_lr)
        total_steps = len(train_loader) * self.config.finetune_epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=self.config.warmup_steps,
            num_training_steps=total_steps
        )

        print(f"总训练步数: {total_steps}")
        print("-" * 50)

        # 损失函数
        criterion = nn.MSELoss()

        best_val_loss = float('inf')
        
        for epoch in range(self.config.finetune_epochs):
            # 训练阶段
            self.model.train()
            total_train_loss = 0

            progress_bar = tqdm(train_loader, desc=f"微调 Epoch {epoch+1}/{self.config.finetune_epochs}")
            
            for batch in progress_bar:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                optimizer.zero_grad()
                
                outputs = self.model(input_ids, attention_mask)
                loss = criterion(outputs.squeeze(), labels)
                
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                total_train_loss += loss.item()
                progress_bar.set_postfix({'train_loss': loss.item()})
            
            avg_train_loss = total_train_loss / len(train_loader)
            
            # 验证阶段
            val_loss, val_metrics = self.evaluate(val_loader)
            
            print(f"Epoch {epoch+1}:")
            print(f"  训练损失: {avg_train_loss:.4f}")
            print(f"  验证损失: {val_loss:.4f}")
            print(f"  验证R²: {val_metrics['r2']:.4f}")
            print(f"  验证MAE: {val_metrics['mae']:.4f}")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                self.save_model()
                print("  保存最佳模型")
    
    def evaluate(self, dataloader):
        """评估模型"""
        self.model.eval()
        total_loss = 0
        predictions = []
        true_labels = []
        
        criterion = nn.MSELoss()
        
        with torch.no_grad():
            for batch in dataloader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                outputs = self.model(input_ids, attention_mask)
                loss = criterion(outputs.squeeze(), labels)
                
                total_loss += loss.item()
                predictions.extend(outputs.squeeze().cpu().numpy())
                true_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(dataloader)
        
        # 计算指标
        predictions = np.array(predictions)
        true_labels = np.array(true_labels)
        
        r2 = r2_score(true_labels, predictions)
        mae = mean_absolute_error(true_labels, predictions)
        rmse = np.sqrt(mean_squared_error(true_labels, predictions))
        
        metrics = {
            'r2': r2,
            'mae': mae,
            'rmse': rmse
        }
        
        return avg_loss, metrics
    
    def save_model(self):
        """保存微调模型"""
        self.model.bert.save_pretrained(self.config.finetune_output_dir)
        torch.save(self.model.state_dict(),
                  os.path.join(self.config.finetune_output_dir, "model.pt"))
        print(f"微调模型已保存到: {self.config.finetune_output_dir}")
    
    def test(self, test_loader):
        """测试模型"""
        print("开始测试...")
        test_loss, test_metrics = self.evaluate(test_loader)
        
        print("测试结果:")
        print(f"  测试损失: {test_loss:.4f}")
        print(f"  测试R²: {test_metrics['r2']:.4f}")
        print(f"  测试MAE: {test_metrics['mae']:.4f}")
        print(f"  测试RMSE: {test_metrics['rmse']:.4f}")
        
        return test_metrics
