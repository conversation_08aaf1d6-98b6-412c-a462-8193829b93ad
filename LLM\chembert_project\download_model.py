#!/usr/bin/env python3
"""
ChemBERTa模型下载脚本
"""

import os

def main():
    """下载ChemBERTa模型"""

    # 检查依赖
    try:
        from transformers import AutoModel, AutoTokenizer, AutoConfig
        import torch
    except ImportError as e:
        print(f"缺少依赖: {e}")
        print("请安装: pip install torch transformers")
        return

    model_name = "DeepChem/ChemBERTa-77M-MLM"
    # 确保模型保存在当前项目目录下
    script_dir = os.path.dirname(os.path.abspath(__file__))
    local_dir = os.path.join(script_dir, "models", "chembert_base")

    print(f"下载模型: {model_name}")
    print(f"保存到: {local_dir}")

    # 创建目录
    os.makedirs(local_dir, exist_ok=True)

    try:
        # 下载配置
        print("下载配置...")
        config = AutoConfig.from_pretrained(model_name)
        config.save_pretrained(local_dir)

        # 下载分词器
        print("下载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(local_dir)

        # 下载模型
        print("下载模型权重...")
        model = AutoModel.from_pretrained(model_name)
        model.save_pretrained(local_dir)

        print("下载完成!")

        # 显示模型信息
        print("\n模型信息:")
        print(f"词汇表大小: {len(tokenizer):,}")
        print(f"隐藏层大小: {config.hidden_size}")
        print(f"注意力头数: {config.num_attention_heads}")
        print(f"隐藏层数: {config.num_hidden_layers}")
        print(f"最大序列长度: {config.max_position_embeddings}")

        # 计算大小
        total_size = 0
        for root, _, files in os.walk(local_dir):
            for file in files:
                total_size += os.path.getsize(os.path.join(root, file))

        print(f"模型大小: {total_size / (1024*1024):.1f} MB")
        print(f"保存位置: {os.path.abspath(local_dir)}")

        # 测试模型
        print("\n测试模型:")
        test_smiles = "CCO"
        inputs = tokenizer(test_smiles, return_tensors="pt")

        with torch.no_grad():
            outputs = model(**inputs)

        print(f"输入: {test_smiles}")
        print(f"输出形状: {outputs.last_hidden_state.shape}")
        print(f"池化输出: {outputs.pooler_output.shape}")

    except Exception as e:
        print(f"下载失败: {e}")

if __name__ == "__main__":
    main()
