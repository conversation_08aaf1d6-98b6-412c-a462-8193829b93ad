=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: ECFP4
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 2228.82 seconds

Best Parameters:
  alpha: 0.1
  colsample_bytree: 0.8
  gamma: 0
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.3962
   RMSE: 0.6295
   MAE: 0.4830
   R2: 0.7756
   MAPE: 1.3404

2. 10-Fold Cross Validation Results:
   Mean R²: 0.3148 ± 0.1316
   Best CV Score: 0.3148
   Individual fold R² scores: ['0.5006', '0.5865', '0.3650', '0.2984', '0.2843', '0.2392', '0.1839', '0.1256', '0.2964', '0.2675']

3. External Validation Set Metrics:
   MSE: 0.9718
   RMSE: 0.9858
   MAE: 0.7617
   R2: 0.4592
   MAPE: 2.3216

Feature Information:
  Numerical Features: []
  Categorical Features: []

