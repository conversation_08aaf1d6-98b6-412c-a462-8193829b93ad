# ==============================================================================
# XGBoost Regression Model (Modified to read existing fingerprints)
# ==============================================================================
# MODIFIED VERSION: This script now reads pre-calculated fingerprints from input files
# instead of generating new ones. This approach:
# 1. Saves computation time by using existing fingerprint data
# 2. Ensures consistency with pre-processed datasets
# 3. Allows direct use of fingerprint columns from Excel files
#
# ==============================================================================
# Part 0: All Imports and Setup
# ==============================================================================

# --- Imports for reading fingerprint data ---
from rdkit import RDLogger

# --- Imports for XGBoost regression ---
import pandas as pd
import numpy as np
import ast
import os
import matplotlib.pyplot as plt
import xgboost as xgb
from sklearn.preprocessing import StandardScaler, OneHotEncoder

from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error
)
from sklearn.model_selection import GridSearchCV

# Suppress RDKit warnings for cleaner output
lg = RDLogger.logger()
lg.setLevel(RDLogger.CRITICAL)


# ==============================================================================
# Part 1: Data Splitting Function (from data_prosesser.py)
# ==============================================================================
# This function is now modified to take a DataFrame and return a modified
# DataFrame, instead of reading/writing from/to files.

def split_dataset(df, train_ratio=0.8, test_ratio=0.2, random_state=42):
    """
    Splits the DataFrame into training set and external validation set with 4:1 ratio.
    Training set will be used for 10-fold cross validation.

    Args:
        df (pd.DataFrame): The input DataFrame.
        train_ratio (float): Training set ratio (default: 0.8 for 4:1 split).
        test_ratio (float): External validation set ratio (default: 0.2 for 4:1 split).
        random_state (int): Random seed for reproducibility.

    Returns:
        pd.DataFrame: The DataFrame with an added 'set' column.
    """
    if abs(train_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("The sum of ratios must be 1.0")

    # 使用sklearn的train_test_split确保更好的随机性和分层
    from sklearn.model_selection import train_test_split

    df_copy = df.copy()

    # 按4:1比例划分数据集，固定随机种子
    _, external_val_indices = train_test_split(
        range(len(df_copy)),
        test_size=test_ratio,
        random_state=random_state,
        shuffle=True
    )

    # 创建set列标记数据集划分
    df_copy['set'] = 'train'
    df_copy.iloc[external_val_indices, df_copy.columns.get_loc('set')] = 'external_val'

    print("Data successfully split into sets (4:1 ratio):")
    print(df_copy['set'].value_counts(normalize=True))
    print(f"Training set: {(df_copy['set'] == 'train').sum()} samples")
    print(f"External validation set: {(df_copy['set'] == 'external_val').sum()} samples")
    return df_copy


# ==============================================================================
# Part 2: Fingerprint Processing Functions (Modified to read existing fingerprints)
# ==============================================================================
# MODIFIED: These functions now read existing fingerprint columns from input data
# instead of generating new fingerprints. This approach:
# 1. Saves computation time by using pre-calculated fingerprints
# 2. Ensures consistency with existing fingerprint data
# 3. Allows direct use of fingerprint columns from input Excel files

# Fingerprint generation functions removed - now reading from existing columns

def add_specific_fingerprint_to_dataframe(df, fingerprint_type):
    """
    Reads the specified fingerprint type from the DataFrame if it exists as a column.
    This function now reads existing fingerprint data instead of generating new ones.

    Args:
        df: Input DataFrame containing fingerprint columns
        fingerprint_type: One of ['Mordred', 'ECFP4', 'MACCS', 'Morgan', 'CMorgan', 'Avalon', 'PubChem']

    Returns:
        DataFrame with the specified fingerprint column processed
    """
    print(f"Looking for {fingerprint_type} fingerprints in input data...")

    df_copy = df.copy()

    # Map fingerprint types to possible column names in the Excel file
    column_mapping = {
        'ECFP4': 'ECFP',
        'Mordred': 'Mordred',
        'MACCS': 'MACCS',
        'Morgan': 'Morgan',
        'CMorgan': 'CMorgan',
        'Avalon': 'Avalon',
        'PubChem': 'PubChem'
    }

    column_name = column_mapping.get(fingerprint_type, fingerprint_type)

    if column_name in df.columns:
        print(f"Found {column_name} fingerprint column in input data.")

        # Process the fingerprint data
        fingerprint_data = []
        for idx, fp_value in enumerate(df_copy[column_name]):
            if pd.isna(fp_value) or fp_value is None:
                fingerprint_data.append([])
                print(f"Warning: NaN fingerprint found at row {idx}, using empty list")
            elif isinstance(fp_value, str):
                try:
                    fp_list = ast.literal_eval(fp_value)
                    if isinstance(fp_list, list):
                        # Clean the fingerprint data
                        fp_clean = []
                        for val in fp_list:
                            if pd.isna(val):
                                fp_clean.append(0.0)
                            else:
                                fp_clean.append(float(val))
                        fingerprint_data.append(fp_clean)
                    else:
                        fingerprint_data.append([])
                        print(f"Warning: Invalid fingerprint format at row {idx}")
                except (ValueError, SyntaxError) as e:
                    fingerprint_data.append([])
                    print(f"Warning: Could not parse fingerprint at row {idx}: {e}")
            elif isinstance(fp_value, list):
                # Already a list, just clean it
                fp_clean = []
                for val in fp_value:
                    if pd.isna(val):
                        fp_clean.append(0.0)
                    else:
                        fp_clean.append(float(val))
                fingerprint_data.append(fp_clean)
            else:
                fingerprint_data.append([])
                print(f"Warning: Unexpected fingerprint type at row {idx}: {type(fp_value)}")

        df_copy[fingerprint_type] = fingerprint_data
        print(f"Successfully loaded {fingerprint_type} fingerprints from input file.")
        print(f"Processed {len(fingerprint_data)} fingerprint entries.")

        # Check for empty fingerprints
        empty_count = sum(1 for fp in fingerprint_data if len(fp) == 0)
        if empty_count > 0:
            print(f"Warning: {empty_count} empty fingerprints found.")

    else:
        print(f"Warning: {column_name} fingerprint column not found in input data.")
        print(f"Available columns: {df.columns.tolist()}")
        # Create empty fingerprint column
        df_copy[fingerprint_type] = [[]] * len(df_copy)

    return df_copy


# Multiple fingerprint function removed - now reading from existing columns


# ==============================================================================
# Part 3: ML Preprocessing and Evaluation Functions (modified for XGBoost regression)
# ==============================================================================

def identify_feature_types(feature_columns, data):
    """
    Identifies numerical, categorical, and mixed features from a list of feature columns.
    Specially handles mixed features like 'Duration'.
    """
    numerical_features = []
    categorical_features = []
    mixed_features = []
    
    for col in feature_columns:
        if col not in data.columns:
            continue
            
        # Try converting to numeric, check for errors
        try:
            # If all values can be converted to numeric, it's a numerical feature
            data[col].astype(float)
            numerical_features.append(col)
        except ValueError:
            # Check if it's a mixed feature with some numeric values
            numeric_mask = pd.to_numeric(data[col], errors='coerce').notna()
            if numeric_mask.any() and not numeric_mask.all():
                mixed_features.append(col)
            else:
                categorical_features.append(col)
    
    print(f"Identified {len(numerical_features)} numerical features: {numerical_features}")
    print(f"Identified {len(categorical_features)} categorical features: {categorical_features}")
    print(f"Identified {len(mixed_features)} mixed features: {mixed_features}")
    
    return numerical_features, categorical_features, mixed_features

def preprocess_data_with_features(data, fingerprint_column, feature_columns):
    """
    Preprocesses data including fingerprints, numerical, categorical, and mixed features.
    """
    data = data.copy()

    # Process fingerprint if present
    if fingerprint_column:
        data[fingerprint_column] = data[fingerprint_column].apply(
            lambda x: ast.literal_eval(x) if isinstance(x, str) else x
        )
        max_length = data[fingerprint_column].apply(len).max()
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(
            lambda x: x + [0] * (max_length - len(x))
        )
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))
        max_length = 0

    # Identify feature types
    numerical_features, categorical_features, mixed_features = identify_feature_types(feature_columns, data)

    # 创建用于编码的纯分类特征列表（不包含混合特征）
    pure_categorical_features = categorical_features.copy()

    # Process mixed features - create numeric versions and categorical indicators
    mixed_medians = {}  # 存储混合特征的中位数，用于测试集处理
    for mixed_feature in mixed_features:
        # Create numeric version
        numeric_col_name = f"{mixed_feature}_numeric"
        data[numeric_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce')

        # Fill missing values with median
        median_value = data[numeric_col_name].median()
        mixed_medians[mixed_feature] = median_value  # 保存中位数
        data.loc[:, numeric_col_name] = data[numeric_col_name].fillna(median_value)

        # Add indicator column
        indicator_col_name = f"{mixed_feature}_is_other"
        data[indicator_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce').isna().astype(int)

        # Add numeric version and indicator to numerical features
        numerical_features.append(numeric_col_name)
        numerical_features.append(indicator_col_name)

        # 将混合特征转换为统一的字符串类型，以便能正确编码
        data[f"{mixed_feature}_str"] = data[mixed_feature].astype(str)
        pure_categorical_features.append(f"{mixed_feature}_str")

    # Process numerical features
    if numerical_features:
        X_num = data[numerical_features].values
    else:
        X_num = np.empty((len(data), 0))

    # Process categorical features
    if pure_categorical_features:
        # One-hot encode each categorical feature
        encoders = {}
        encoded_features = []

        for cat_feature in pure_categorical_features:
            # Fit encoder on this feature
            encoder = OneHotEncoder(sparse_output=False, handle_unknown='ignore')
            feature_values = data[cat_feature].values.reshape(-1, 1)
            encoded_feature = encoder.fit_transform(feature_values)

            # Store encoder for later use
            encoders[cat_feature] = encoder
            encoded_features.append(encoded_feature)

        # Combine all encoded features
        if encoded_features:
            X_cat = np.hstack(encoded_features)
        else:
            X_cat = np.empty((len(data), 0))
    else:
        X_cat = np.empty((len(data), 0))
        encoders = {}

    # Combine fingerprints, numerical and categorical features
    X = np.hstack([X_fp, X_num, X_cat])

    return X, max_length, numerical_features, pure_categorical_features, encoders, mixed_features, mixed_medians

def preprocess_test_with_features(data, fingerprint_column, numerical_features,
                                  categorical_features, max_length, encoders, mixed_features, mixed_medians=None):
    """
    Preprocesses test data using artifacts from training, including mixed features.
    """
    if data.empty:
        return np.array([[]])

    data = data.copy()

    # Process fingerprint if present
    if fingerprint_column:
        data[fingerprint_column] = data[fingerprint_column].apply(
            lambda x: ast.literal_eval(x) if isinstance(x, str) else x
        )
        data.loc[:, fingerprint_column] = data[fingerprint_column].apply(
            lambda x: x + [0] * (max_length - len(x))
        )
        X_fp = np.array(data[fingerprint_column].tolist())
    else:
        X_fp = np.empty((len(data), 0))

    # Process numerical features
    if numerical_features:
        # 创建缺失的数值特征列
        for feat in numerical_features:
            if feat not in data.columns:
                data[feat] = 0
        X_num = data[numerical_features].values
    else:
        X_num = np.empty((len(data), 0))

    # Process mixed features - apply same transformations as training
    for mixed_feature in mixed_features:
        numeric_col_name = f"{mixed_feature}_numeric"
        data[numeric_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce')

        # 使用训练集中保存的中位数填充
        median_value = mixed_medians.get(mixed_feature) if mixed_medians else data[numeric_col_name].median()
        data.loc[:, numeric_col_name] = data[numeric_col_name].fillna(median_value)

        indicator_col_name = f"{mixed_feature}_is_other"
        data[indicator_col_name] = pd.to_numeric(data[mixed_feature], errors='coerce').isna().astype(int)

        # 创建字符串版本
        data[f"{mixed_feature}_str"] = data[mixed_feature].astype(str)

    # Process categorical features using stored encoders
    if categorical_features:
        encoded_features = []

        for cat_feature in categorical_features:
            # 确保特征存在
            if cat_feature not in data.columns and cat_feature.endswith('_str'):
                orig_feature = cat_feature.replace('_str', '')
                if orig_feature in data.columns:
                    data[cat_feature] = data[orig_feature].astype(str)

            # Use stored encoder for this feature
            if cat_feature in encoders:
                encoder = encoders[cat_feature]
                feature_values = data[cat_feature].values.reshape(-1, 1)
                encoded_feature = encoder.transform(feature_values)
                encoded_features.append(encoded_feature)

        # Combine all encoded features
        if encoded_features:
            X_cat = np.hstack(encoded_features)
        else:
            X_cat = np.empty((len(data), 0))
    else:
        X_cat = np.empty((len(data), 0))

    # Combine fingerprints, numerical and categorical features
    X = np.hstack([X_fp, X_num, X_cat])

    return X

def evaluate_regression(y_true, y_pred, set_name, output_dir):
    """Evaluates regression model and saves plots."""
    print(f"\n===== {set_name} Evaluation Report =====")

    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    mape = mean_absolute_percentage_error(y_true, y_pred)

    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    print(f"MAPE: {mape:.4f}")

    # Create prediction vs actual plot
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.scatter(y_true, y_pred, alpha=0.6)
    plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
    plt.xlabel('Actual Values')
    plt.ylabel('Predicted Values')
    plt.title(f'{set_name} - Predicted vs Actual\nR² = {r2:.4f}')

    plt.subplot(1, 2, 2)
    residuals = y_true - y_pred
    plt.scatter(y_pred, residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('Predicted Values')
    plt.ylabel('Residuals')
    plt.title(f'{set_name} - Residual Plot')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{set_name}_regression_plots_{fp_column}.png'))
    plt.close()

    return {'MSE': mse, 'RMSE': rmse, 'MAE': mae, 'R2': r2, 'MAPE': mape}

def extract_cv_results_from_gridsearch(grid_search, cv_folds=10):
    """从GridSearchCV结果中提取详细的交叉验证指标"""
    print(f"\n===== {cv_folds}-Fold Cross Validation Results from GridSearch =====")

    best_idx = grid_search.best_index_
    cv_results = grid_search.cv_results_

    # 提取最佳模型的交叉验证分数
    r2_scores = []
    for i in range(cv_folds):
        score_key = f'split{i}_test_score'
        if score_key in cv_results:
            r2_scores.append(cv_results[score_key][best_idx])

    r2_scores = np.array(r2_scores)

    print(f"Best parameters: {grid_search.best_params_}")
    print(f"Individual fold R² scores: {[f'{score:.4f}' for score in r2_scores]}")
    print(f"Mean R²: {r2_scores.mean():.4f} ± {r2_scores.std():.4f}")
    print(f"Best CV Score (from GridSearchCV): {grid_search.best_score_:.4f}")

    return {
        'R2_mean': r2_scores.mean(),
        'R2_std': r2_scores.std(),
        'R2_scores': r2_scores,
        'best_score': grid_search.best_score_
    }


# ==============================================================================
# Part 4: Main Execution Block
# ==============================================================================

if __name__ == '__main__':

    # --- 1. Configuration ---
    input_file = '../data/filtered_molecules_tanimoto_0.85.xlsx'  # 输入文件（包含预计算的指纹）
    fp_column = 'PubChem'             # The fingerprint to use for modeling (Mordred, ECFP, MACCS, Morgan, CMorgan, Avalon, PubChem)
    feature_columns = []                      # No additional features - only using fingerprints
    label_column = 'pEC50_new'                   # The target variable for regression
    output_dir = 'prediction/XGBoost_Regression'  # Directory to save plots and results

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # --- 2. Data Loading and Preprocessing Pipeline ---
    print("--- Starting QSAR XGBoost Regression Pipeline ---")

    print("\nStep 1: Loading initial data...")
    try:
        df_raw = pd.read_excel(input_file)
        print(f"Data loaded successfully. Shape: {df_raw.shape}")

        # Check for NaN values in target variable
        print(f"\nTarget variable ({label_column}) statistics:")
        print(df_raw[label_column].describe())
        nan_count = df_raw[label_column].isna().sum()
        print(f"NaN values in target variable: {nan_count}")

        if nan_count > 0:
            print(f"Removing {nan_count} rows with NaN target values...")
            df_raw = df_raw.dropna(subset=[label_column])
            print(f"Data shape after removing NaN targets: {df_raw.shape}")

    except FileNotFoundError:
        print(f"Error: Input file not found at '{input_file}'")
        exit()

    print("\nStep 2: Loading fingerprints from input data...")
    # Read the specified fingerprint type from the input file
    data_with_fp = add_specific_fingerprint_to_dataframe(df_raw, fp_column)

    print("\nStep 3: Splitting data into training set and external validation set (4:1 ratio)...")
    data = split_dataset(data_with_fp, train_ratio=0.8, test_ratio=0.2, random_state=42)

    print("\n--- Preprocessing and Model Training ---")

    # --- 3. ML Data Preparation ---
    # 分开训练集和外部验证集
    train_data = data[data['set'] == 'train']
    external_val_data = data[data['set'] == 'external_val']

    print(f"Training data set shape: {train_data.shape}")
    print(f"External validation data set shape: {external_val_data.shape}")

    # 处理训练集，包括特征编码
    X_train, max_length, numerical_features, categorical_features, encoders, mixed_features, mixed_medians = preprocess_data_with_features(
        train_data, fp_column, feature_columns
    )

    # 处理外部验证集
    X_external_val = preprocess_test_with_features(
        external_val_data, fp_column, numerical_features, categorical_features, max_length, encoders, mixed_features, mixed_medians
    )

    y_train = train_data[label_column].values
    y_external_val = external_val_data[label_column].values

    # Check for NaN values and remove them
    print("\nChecking for NaN values...")

    # Remove NaN values from training data
    if np.isnan(y_train).any():
        print("Warning: Found NaN values in training targets. Removing affected samples.")
        valid_indices = ~np.isnan(y_train)
        X_train = X_train[valid_indices]
        y_train = y_train[valid_indices]
        print(f"Training set size after removing NaN targets: {len(y_train)}")

    if np.isnan(y_external_val).any():
        print("Warning: Found NaN values in validation targets. Removing affected samples.")
        valid_indices = ~np.isnan(y_external_val)
        X_external_val = X_external_val[valid_indices]
        y_external_val = y_external_val[valid_indices]
        print(f"Validation set size after removing NaN targets: {len(y_external_val)}")

    # Final check for NaN values in features
    if np.isnan(X_train).any():
        print("Warning: NaN values found in training features. Replacing with 0.")
        X_train = np.nan_to_num(X_train, nan=0.0)

    if np.isnan(X_external_val).any():
        print("Warning: NaN values found in validation features. Replacing with 0.")
        X_external_val = np.nan_to_num(X_external_val, nan=0.0)

    # 注意：XGBoost是基于树的模型，通常不需要特征缩放
    # 为了保持代码一致性，这里仍然进行缩放，但可以考虑移除以提高性能
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_external_val_scaled = scaler.transform(X_external_val)

    # --- 4. Model Training and Hyperparameter Tuning on training set ---
    print("\nTraining XGBoost Regressor with 10-fold GridSearchCV on training set...")
    xgb_regressor = xgb.XGBRegressor(
        random_state=42,
        n_jobs=1,  # 限制XGBoost内部并行以避免内存问题
        verbosity=0,  # Reduce XGBoost output
        tree_method='hist'  # 使用CPU优化的方法
    )

    # 精简的参数网格 - 减少组合数量以加快训练速度
    param_grid = {
        'n_estimators': [100, 200],           # 2个选项
        'max_depth': [4, 6],                  # 2个选项
        'learning_rate': [0.05, 0.1],         # 2个选项
        'subsample': [0.8],                   # 1个选项
        'colsample_bytree': [0.8],            # 1个选项
        'gamma': [0],                         # 1个选项
        'alpha': [0],                         # 1个选项
        'lambda': [1]                         # 1个选项
    }
    # 总组合数：2×2×2×1×1×1×1×1 = 8个组合，80次拟合

    # 计算实际的参数组合数量
    param_combinations = 1
    for param_values in param_grid.values():
        param_combinations *= len(param_values)

    print(f"参数组合数量: {param_combinations}")
    print(f"预计总拟合次数: {param_combinations * 10} (10折交叉验证)")

    # 强制垃圾回收以释放内存
    import gc
    gc.collect()

    # 使用10折交叉验证进行超参数调优 - 限制并行进程数以避免内存问题
    grid_search = GridSearchCV(xgb_regressor, param_grid, cv=10, scoring='r2', n_jobs=2, verbose=1)
    grid_search.fit(X_train_scaled, y_train)

    print(f"\nBest parameters found: {grid_search.best_params_}")
    print(f"Best cross-validation R² score: {grid_search.best_score_:.4f}")
    best_model = grid_search.best_estimator_

    # --- 5. 提取并显示10折交叉验证结果 ---
    cv_results = extract_cv_results_from_gridsearch(grid_search, cv_folds=10)

    # --- 6. Model Evaluation ---
    print("\n--- Model Performance Evaluation ---")

    # Training Set Performance
    print("\n1. Training Set Performance:")
    y_train_pred = best_model.predict(X_train_scaled)
    train_metrics = evaluate_regression(y_train, y_train_pred, "Training_Set", output_dir)

    # 10-Fold Cross Validation Performance (already displayed above, but summarize here)
    print(f"\n2. 10-Fold Cross Validation Performance:")
    print(f"   Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}")
    print(f"   Best CV Score: {cv_results['best_score']:.4f}")

    # External Validation Set Performance
    print("\n3. External Validation Set Performance:")
    y_external_val_pred = best_model.predict(X_external_val_scaled)
    external_val_metrics = evaluate_regression(y_external_val, y_external_val_pred, "External_Validation", output_dir)

    # --- 7. Final Prediction and Output ---
    print("\nGenerating predictions for the entire dataset...")
    # 处理所有数据用于最终预测
    X_all = preprocess_test_with_features(
        data, fp_column, numerical_features, categorical_features, max_length, encoders, mixed_features, mixed_medians
    )
    X_all_scaled = scaler.transform(X_all)

    data['prediction'] = best_model.predict(X_all_scaled)

    # Calculate residuals for the entire dataset
    data['residuals'] = data[label_column] - data['prediction']

    # 创建用于保存的数据副本，移除分子指纹列以节省文件大小
    data_to_save = data.copy()
    if fp_column in data_to_save.columns:
        data_to_save = data_to_save.drop(columns=[fp_column])
        print(f"注意：为节省文件大小，已从输出文件中移除 {fp_column} 指纹列")

    output_filename = os.path.join(output_dir, f'final_predictions_{fp_column}.xlsx')
    data_to_save.to_excel(output_filename, index=False)

    # Save summary results
    summary_results = {
        'Cross_Validation': cv_results,
        'Training_Metrics': train_metrics,
        'External_Validation_Metrics': external_val_metrics,
        'Best_Parameters': grid_search.best_params_
    }

    # Save summary to text file
    summary_filename = os.path.join(output_dir, f'model_summary_{fp_column}.txt')
    with open(summary_filename, 'w', encoding='utf-8') as f:
        f.write("=== XGBoost Regression Model Summary ===\n\n")
        f.write(f"Input File: {input_file}\n")
        f.write(f"Fingerprint: {fp_column}\n")
        f.write(f"Target Variable: {label_column}\n\n")

        f.write("Best Parameters:\n")
        for param, value in grid_search.best_params_.items():
            f.write(f"  {param}: {value}\n")
        f.write("\n")

        f.write("=== Model Performance Results ===\n\n")

        f.write("1. Training Set Metrics:\n")
        for metric, value in train_metrics.items():
            f.write(f"   {metric}: {value:.4f}\n")
        f.write("\n")

        f.write("2. 10-Fold Cross Validation Results:\n")
        f.write(f"   Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}\n")
        f.write(f"   Best CV Score: {cv_results['best_score']:.4f}\n")
        f.write(f"   Individual fold R² scores: {[f'{score:.4f}' for score in cv_results['R2_scores']]}\n\n")

        f.write("3. External Validation Set Metrics:\n")
        for metric, value in external_val_metrics.items():
            f.write(f"   {metric}: {value:.4f}\n")
        f.write("\n")

        # 添加特征处理信息
        f.write("Feature Information:\n")
        f.write(f"  Numerical Features: {numerical_features}\n")
        f.write(f"  Categorical Features: {categorical_features}\n")
        for cat_feature in categorical_features:
            if cat_feature in encoders:
                categories = encoders[cat_feature].categories_[0]
                f.write(f"    {cat_feature} Categories: {list(categories)}\n")
        f.write("\n")

    # --- 8. Final Summary ---
    print(f"\n=== FINAL MODEL PERFORMANCE SUMMARY ===")
    print(f"Training Set R²: {train_metrics['R2']:.4f}")
    print(f"10-Fold CV Mean R²: {cv_results['R2_mean']:.4f} ± {cv_results['R2_std']:.4f}")
    print(f"External Validation R²: {external_val_metrics['R2']:.4f}")
    print(f"\nTraining Set RMSE: {train_metrics['RMSE']:.4f}")
    print(f"External Validation RMSE: {external_val_metrics['RMSE']:.4f}")

    print(f"\n--- XGBoost Pipeline Finished ---")
    print(f"Plots and final predictions saved in '{output_dir}' directory.")
    print(f"Final output file: '{output_filename}'")
    print(f"Model summary: '{summary_filename}'")
