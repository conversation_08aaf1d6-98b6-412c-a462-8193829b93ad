=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: Morgan
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 2129.15 seconds

Best Parameters:
  alpha: 0.1
  colsample_bytree: 1.0
  gamma: 0
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.4769
   RMSE: 0.6906
   MAE: 0.5254
   R2: 0.7300
   MAPE: 1.4752

2. 10-Fold Cross Validation Results:
   Mean R²: 0.2924 ± 0.1585
   Best CV Score: 0.2924
   Individual fold R² scores: ['0.5556', '0.6267', '0.3052', '0.2015', '0.2420', '0.2169', '0.1145', '0.1565', '0.2424', '0.2626']

3. External Validation Set Metrics:
   MSE: 1.0247
   RMSE: 1.0123
   MAE: 0.7778
   R2: 0.4298
   MAPE: 2.3304

Feature Information:
  Numerical Features: []
  Categorical Features: []

