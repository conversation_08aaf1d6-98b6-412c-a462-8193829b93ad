import os

class Config:
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # 数据路径
    data_dir = os.path.join(project_root, "data")
    pretrain_data = os.path.join(data_dir, "Zinc.csv")
    finetune_data = os.path.join(data_dir, "DMAT.csv")

    # 运行配置
    run_mode = "finetune"  # "both": 完整流程, "pretrain": 仅预训练, "finetune": 仅微调
    skip_pretrain = False  # True: 跳过预训练直接微调
    use_local_model = True  # True: 使用本地模型, False: 在线下载

    # 模型配置
    model_name = os.path.join(project_root, "models", "chembert_base")
    max_length = 256
    
    # 预训练配置
    pretrain_batch_size = 32
    pretrain_epochs = 10
    pretrain_lr = 5e-5
    pretrain_output_dir = os.path.join(project_root, "models", "pretrained")

    # 微调配置
    finetune_batch_size = 16
    finetune_epochs = 20
    finetune_lr = 0.001
    finetune_output_dir = os.path.join(project_root, "models", "finetuned")
    target_column = "pDMImEC50"
    
    # 训练配置
    warmup_steps = 500
    logging_steps = 100
    save_steps = 1000
    eval_steps = 500

    # 设备配置（将在运行时动态设置）
    device = "cpu"  # 默认CPU，运行时会检测并更新

    # 随机种子
    seed = 42
