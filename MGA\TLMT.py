import numpy as np
from utils import build_dataset
import torch
from torch.optim import <PERSON>
from torch.utils.data import DataLoader
from utils.MY_GNN import collate_molgraphs, EarlyStopping, run_a_train_epoch_heterogeneous, \
    run_an_eval_epoch_heterogeneous_r2, run_an_eval_predmerge, set_random_seed, MGA, pos_weight, run_an_eval_epoch_pih, \
    run_an_eval_epoch_heterogeneous_rmse, run_an_eval_epoch_heterogeneous_mse, run_an_eval_epoch_heterogeneous_mae

import time
import pandas as pd
start = time.time()


# fix parameters of model
args = {}
args['device'] = "cuda" if torch.cuda.is_available() else "cpu"
args['atom_data_field'] = 'atom'
args['bond_data_field'] = 'etype'
args['classification_AROC'] = 'roc_auc'
###回归指标
args['regression_r2'] = 'r2'
args['regression_mse'] = 'mse'
args['regression_mae'] = 'mae'
args['regression_rmse'] = 'rmse'
# model parameter
args['num_epochs'] = 500
args['patience'] = 50
args['batch_size'] = 512
args['mode'] = 'higher'
args['in_feats'] = 40
args['rgcn_hidden_feats'] = [256, 128]
args['classifier_hidden_feats'] = 64
args['rgcn_drop_out'] = 0.3
args['drop_out'] = 0.2
args['lr'] = 3
args['weight_decay'] = 5
args['loop'] = True

# pretrained_model
args['pretrained_model'] = 'LogKOW预训练-256128256_early_stop.pth'

# task name (model name)
args['task_name'] = 'FineTuning_pDMImEC50'  # change
args['data_name'] = 'DMAT'  # change
args['times'] = 1

# selected task, generate select task index, task class, and classification_num
args['select_task_list'] = ['pDMImEC50']   # change
args['select_task_index'] = []
args['classification_num'] = 0
args['regression_num'] = 0
args['all_task_list'] = ['logKow', 'pDMImEC50']  # change
# generate select task index
for index, task in enumerate(args['all_task_list']):
    if task in args['select_task_list']:
        args['select_task_index'].append(index)
# generate classification_num
for task in args['select_task_list']:
    if task in []:
        args[ 'classification_num'] = args['classification_num'] + 1
    if task in ['logKow', 'pDMImEC50']:
        args['regression_num'] = args['regression_num'] + 1
# generate classification_num
if args['classification_num'] != 0 and args['regression_num'] != 0:
    args['task_class'] = 'classification_regression'
if args['classification_num'] != 0 and args['regression_num'] == 0:
    args['task_class'] = 'classification'
if args['classification_num'] == 0 and args['regression_num'] != 0:
    args['task_class'] = 'regression'

args['bin_path'] = 'data/' + args['data_name'] + '.bin'
args['group_path'] = 'data/' + args['data_name'] + '_group.csv'

result_pd = pd.DataFrame(columns=args['select_task_list']+['group'] + args['select_task_list']+['group']
                         + args['select_task_list']+['group'])
all_times_train_result = []
all_times_val_result = []
all_times_test_result = []
for time_id in range(args['times']):
    set_random_seed(2020+time_id)
    one_time_train_result = []
    one_time_val_result = []
    one_time_test_result = []
    print('***************************************************************************************************')
    print('{}, {}/{} time'.format(args['task_name'], time_id+1, args['times']))
    print('***************************************************************************************************')
    train_set, val_set, test_set, task_number = build_dataset.load_graph_from_csv_bin_for_splited(
        bin_path=args['bin_path'],
        group_path=args['group_path'],
        select_task_index=args['select_task_index']
    )
    from torch.utils.data import ConcatDataset
    all_dataset = ConcatDataset([train_set, val_set, test_set])
    all_dataloader = DataLoader(dataset=all_dataset, batch_size=args['batch_size'], collate_fn=collate_molgraphs, shuffle=False)  # 预测时不需要打乱顺序
    print("Molecule graph generation is complete !")
    train_loader = DataLoader(dataset=train_set,
                              batch_size=args['batch_size'],
                              shuffle=True,
                              collate_fn=collate_molgraphs)

    val_loader = DataLoader(dataset=val_set,
                            batch_size=args['batch_size'],
                            shuffle=True,
                            collate_fn=collate_molgraphs)

    test_loader = DataLoader(dataset=test_set,
                             batch_size=args['batch_size'],
                             collate_fn=collate_molgraphs)
    

    pos_weight_np = pos_weight(train_set, classification_num=args['classification_num'])
    loss_criterion_c = torch.nn.BCEWithLogitsLoss(reduction='none', pos_weight=pos_weight_np.to(args['device']))
    loss_criterion_r = torch.nn.MSELoss(reduction='none')
    model = MGA(in_feats=args['in_feats'], rgcn_hidden_feats=args['rgcn_hidden_feats'],
                   n_tasks=task_number, rgcn_drop_out=args['rgcn_drop_out'],
                   classifier_hidden_feats=args['classifier_hidden_feats'], dropout=args['drop_out'],
                   loop=args['loop'])
    optimizer = Adam(model.parameters(), lr=10**-args['lr'], weight_decay=10**-args['weight_decay'])
    stopper = EarlyStopping(pretrained_model=args['pretrained_model'], patience=args['patience'], task_name=args['task_name'], mode=args['mode'])
    model.to(args['device'])

    # load pretrained model and freeze RGCN layer
    print("Load pre-trained model")
    stopper.load_pretrained_model(model)
    pretrained_parameters = ['gnn_layers.0.graph_conv_layer.weight',
                             'gnn_layers.0.graph_conv_layer.h_bias',
                             'gnn_layers.0.graph_conv_layer.loop_weight',
                             'gnn_layers.0.res_connection.weight',
                             'gnn_layers.0.res_connection.bias',
                             'gnn_layers.0.bn_layer.weight',
                             'gnn_layers.0.bn_layer.bias',
                             'gnn_layers.0.bn_layer.running_mean',
                             'gnn_layers.0.bn_layer.running_var',
                             'gnn_layers.0.bn_layer.num_batches_tracked',
                             'gnn_layers.1.graph_conv_layer.weight',
                             'gnn_layers.1.graph_conv_layer.h_bias',
                             'gnn_layers.1.graph_conv_layer.loop_weight',
                             'gnn_layers.1.res_connection.weight',
                             'gnn_layers.1.res_connection.bias',
                             'gnn_layers.1.bn_layer.weight',
                             'gnn_layers.1.bn_layer.bias',
                             'gnn_layers.1.bn_layer.running_mean',
                             'gnn_layers.1.bn_layer.running_var',
                             'gnn_layers.1.bn_layer.num_batches_tracked']
    for i, child in enumerate(model.children()):
        if i == 0:
            for param in child.parameters():
                param.requires_grad = False
    for epoch in range(args['num_epochs']):
        # Train
        run_a_train_epoch_heterogeneous(args, epoch, model, train_loader, loss_criterion_c, loss_criterion_r, optimizer)
        # Validation and early stop
        validation_result = run_an_eval_epoch_heterogeneous_r2(args, model, val_loader)
        val_r2 = np.mean(validation_result)
        early_stop = stopper.step(val_r2, model)
        print('epoch {:d}/{:d}, validation {:.4f}, best validation {:.4f}'.format(
            epoch + 1, args['num_epochs'],
            val_r2,  stopper.best_score)+' validation result:', validation_result)
        if early_stop:
            break
    stopper.load_checkpoint(model)

    test_r2 = run_an_eval_epoch_heterogeneous_r2(args, model, test_loader)
    train_r2 = run_an_eval_epoch_heterogeneous_r2(args, model, train_loader)
    val_r2 = run_an_eval_epoch_heterogeneous_r2(args, model, val_loader)

    test_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, test_loader)
    train_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, train_loader)
    val_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, val_loader)

    test_mse = run_an_eval_epoch_heterogeneous_mse(args, model, test_loader)
    train_mse = run_an_eval_epoch_heterogeneous_mse(args, model, train_loader)
    val_mse = run_an_eval_epoch_heterogeneous_mse(args, model, val_loader)

    test_mae = run_an_eval_epoch_heterogeneous_mae(args, model, test_loader)
    train_mae = run_an_eval_epoch_heterogeneous_mae(args, model, train_loader)
    val_mae = run_an_eval_epoch_heterogeneous_mae(args, model, val_loader)

    # deal result
    result = train_r2 + ['training'] + val_r2 + ['valid'] + test_r2 + ['test']
    result_pd.loc[time_id] = result
    print('********************************{}, {}_times_result*******************************'.format(args['task_name'], time_id+1))
    print("training_result:", train_r2, train_rmse, train_mse, train_mae)
    print("val_result:", val_r2, val_rmse, val_mse, val_mae)
    print("test_result:", test_r2, test_rmse, test_mse, test_mae)

    run_an_eval_epoch_pih(args, model, all_dataloader, output_path='jieguo/'+args['task_name']+'prediction.csv')

result_pd.to_csv('result/'+args['task_name']+'_result.csv', index=None)
elapsed = (time.time() - start)
m, s = divmod(elapsed, 60)
h, m = divmod(m, 60)
print("Time used:", "{:d}:{:d}:{:d}".format(int(h), int(m), int(s)))