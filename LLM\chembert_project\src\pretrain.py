import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer, get_linear_schedule_with_warmup
from torch.optim import <PERSON><PERSON>
from tqdm import tqdm
import os
import random
import numpy as np

class MLMPretrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.device)

        # 设置随机种子
        self.set_seed()

        # 初始化模型和分词器
        self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)
        self.model = AutoModel.from_pretrained(config.model_name)
        self.model.to(self.device)

        # MLM头
        self.mlm_head = nn.Linear(self.model.config.hidden_size, self.tokenizer.vocab_size)
        self.mlm_head.to(self.device)

    def set_seed(self):
        random.seed(self.config.seed)
        np.random.seed(self.config.seed)
        torch.manual_seed(self.config.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(self.config.seed)
    
    def mask_tokens(self, inputs, mask_prob=0.15):
        """随机掩码token用于MLM任务"""
        labels = inputs.clone()
        device = inputs.device  # 获取输入张量的设备

        # 创建掩码概率矩阵
        probability_matrix = torch.full(labels.shape, mask_prob, device=device)
        special_tokens_mask = [
            self.tokenizer.get_special_tokens_mask(val, already_has_special_tokens=True)
            for val in labels.tolist()
        ]
        probability_matrix.masked_fill_(torch.tensor(special_tokens_mask, dtype=torch.bool, device=device), value=0.0)

        masked_indices = torch.bernoulli(probability_matrix).bool()
        labels[~masked_indices] = -100  # 只计算被掩码的token的损失

        # 80%的时间用[MASK]替换
        indices_replaced = torch.bernoulli(torch.full(labels.shape, 0.8, device=device)).bool() & masked_indices
        inputs[indices_replaced] = self.tokenizer.convert_tokens_to_ids(self.tokenizer.mask_token)

        # 10%的时间用随机token替换
        indices_random = torch.bernoulli(torch.full(labels.shape, 0.5, device=device)).bool() & masked_indices & ~indices_replaced
        random_words = torch.randint(len(self.tokenizer), labels.shape, dtype=torch.long, device=device)
        inputs[indices_random] = random_words[indices_random]

        return inputs, labels
    
    def train(self, dataloader):
        """执行预训练"""
        os.makedirs(self.config.pretrain_output_dir, exist_ok=True)

        # 显示预训练参数
        print("预训练参数:")
        print(f"  学习率: {self.config.pretrain_lr}")
        print(f"  批次大小: {self.config.pretrain_batch_size}")
        print(f"  训练轮数: {self.config.pretrain_epochs}")
        print(f"  预热步数: {self.config.warmup_steps}")
        print(f"  训练批次数: {len(dataloader)}")
        print(f"  设备: {self.device}")
        print(f"  输出目录: {self.config.pretrain_output_dir}")
        print("-" * 50)

        # 优化器和调度器
        optimizer = AdamW(
            list(self.model.parameters()) + list(self.mlm_head.parameters()),
            lr=self.config.pretrain_lr
        )

        total_steps = len(dataloader) * self.config.pretrain_epochs
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=self.config.warmup_steps,
            num_training_steps=total_steps
        )

        print(f"总训练步数: {total_steps}")
        print("-" * 50)

        # 损失函数
        criterion = nn.CrossEntropyLoss()
        
        self.model.train()
        self.mlm_head.train()
        
        for epoch in range(self.config.pretrain_epochs):
            total_loss = 0
            progress_bar = tqdm(dataloader, desc=f"预训练 Epoch {epoch+1}/{self.config.pretrain_epochs}")
            
            for batch in progress_bar:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                
                # 掩码处理
                masked_input_ids, labels = self.mask_tokens(input_ids.clone())
                labels = labels.to(self.device)
                
                # 前向传播
                outputs = self.model(input_ids=masked_input_ids, attention_mask=attention_mask)
                sequence_output = outputs.last_hidden_state
                prediction_scores = self.mlm_head(sequence_output)
                
                # 计算损失
                loss = criterion(prediction_scores.view(-1, self.tokenizer.vocab_size), labels.view(-1))
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                total_loss += loss.item()
                progress_bar.set_postfix({'loss': loss.item()})
            
            avg_loss = total_loss / len(dataloader)
            print(f"Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
        
        # 保存模型
        self.save_model()
    
    def save_model(self):
        """保存预训练模型"""
        self.model.save_pretrained(self.config.pretrain_output_dir)
        self.tokenizer.save_pretrained(self.config.pretrain_output_dir)
        torch.save(self.mlm_head.state_dict(),
                  os.path.join(self.config.pretrain_output_dir, "mlm_head.pt"))
        print(f"预训练模型已保存到: {self.config.pretrain_output_dir}")
