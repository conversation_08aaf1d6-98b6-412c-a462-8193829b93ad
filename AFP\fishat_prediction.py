#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
鱼类急性毒性回归预测脚本
专门用于对外部输入文件进行回归预测

功能:
    1. 单个SMILES预测
    2. CSV文件批量预测
    3. Excel文件批量预测
"""

import os
import torch
import numpy as np
import pandas as pd
from rdkit import Chem
import warnings
warnings.filterwarnings('ignore')

# 导入模块
from RegressionFP import prepare_smiles_data

# =============================================================================
# 预测参数设置区域 - 在这里修改您的预测需求
# =============================================================================

# 模型路径
MODEL_PATH = '../save_model/best_model_fold_0.pt'

# 预测模式选择：'single', 'csv', 'excel'
PREDICTION_MODE = 'excel'

# 单个SMILES预测参数
SINGLE_SMILES = 'CCO'  # 要预测的SMILES

# CSV模式参数
CSV_INPUT_FILE = '../data/pre_compounds.csv'  # CSV输入文件路径
CSV_OUTPUT_FILE = '../prediction/pre_compounds_prediction.csv'  # CSV输出文件路径

# Excel模式参数
EXCEL_INPUT_FILE = '../data/pre_compounds.xlsx'  # Excel输入文件路径
EXCEL_OUTPUT_FILE = '../prediction/pre_compounds_prediction.xlsx'  # Excel输出文件路径

# 批处理参数
BATCH_SIZE = 256  # 批处理大小

# =============================================================================

class RegressionPredictor:
    """鱼类急性毒性回归预测器"""

    def __init__(self, model_path):
        """初始化预测器"""
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._load_model()

    def _load_model(self):
        """加载模型和相关组件"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        print(f"🔄 加载模型: {self.model_path}")

        checkpoint = torch.load(self.model_path, map_location=self.device)

        self.model = checkpoint['model']
        self.feature_dicts = checkpoint['feature_dicts']
        self.target_column = checkpoint.get('target_column', 'LC50')

        self.model.eval()
        self.model.to(self.device)

        print(f"✅ 模型加载成功 (设备: {self.device})")

    def _validate_smiles(self, smiles):
        """验证SMILES字符串"""
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return False, "无效的SMILES字符串"

            canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)

            if canonical_smiles not in self.feature_dicts['smiles_to_atom_mask']:
                return False, f"SMILES不在训练数据中"

            return True, canonical_smiles
        except Exception as e:
            return False, f"SMILES处理错误: {str(e)}"

    def predict_single(self, smiles):
        """预测单个SMILES"""
        sample_data = {'smiles': smiles}
        return self.predict_single_with_data(sample_data)

    def predict_single_with_data(self, sample_data):
        """使用完整数据字典预测单个SMILES"""
        smiles = sample_data['smiles']
        is_valid, result = self._validate_smiles(smiles)
        if not is_valid:
            return {**sample_data, 'error': result}

        canonical_smiles = result

        try:
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                [canonical_smiles], self.feature_dicts
            )

            x_atom = torch.Tensor(x_atom).to(self.device)
            x_bonds = torch.Tensor(x_bonds).to(self.device)
            x_atom_index = torch.LongTensor(x_atom_index).to(self.device)
            x_bond_index = torch.LongTensor(x_bond_index).to(self.device)
            x_mask = torch.Tensor(x_mask).to(self.device)

            with torch.no_grad():
                _, mol_prediction = self.model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                predicted_value = float(mol_prediction.squeeze().cpu().numpy())

            result = {
                'smiles': smiles,
                'canonical_smiles': canonical_smiles,
                'predicted_value': predicted_value,
                'error': None
            }

            return result

        except Exception as e:
            return {**sample_data, 'error': f"预测错误: {str(e)}"}

    def predict_batch(self, df, batch_size=256):
        """批量预测"""
        print(f"🔄 开始批量预测，共 {len(df)} 个样本")

        required_columns = ['smiles']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"输入数据缺少必要列: {missing_columns}")

        result_df = df.copy()
        result_df['canonical_smiles'] = ''
        result_df['predicted_value'] = np.nan
        result_df['error'] = ''

        valid_indices = []
        valid_canonical_smiles = []

        for idx, row in df.iterrows():
            is_valid, result = self._validate_smiles(row['smiles'])
            if is_valid:
                result_df.loc[idx, 'canonical_smiles'] = result
                valid_indices.append(idx)
                valid_canonical_smiles.append(result)
            else:
                result_df.loc[idx, 'error'] = result

        print(f"✅ 有效样本: {len(valid_indices)}/{len(df)}")

        if len(valid_indices) == 0:
            return result_df

        try:
            valid_df = result_df.loc[valid_indices].copy()
            valid_df['smiles'] = valid_canonical_smiles

            batch_list = [valid_indices[i:i+batch_size] for i in range(0, len(valid_indices), batch_size)]

            for batch_idx, batch_indices in enumerate(batch_list):
                batch_df = valid_df.loc[batch_indices].reset_index(drop=True)
                batch_smiles = batch_df['smiles'].values

                x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                    batch_smiles, self.feature_dicts
                )

                x_atom = torch.Tensor(x_atom).to(self.device)
                x_bonds = torch.Tensor(x_bonds).to(self.device)
                x_atom_index = torch.LongTensor(x_atom_index).to(self.device)
                x_bond_index = torch.LongTensor(x_bond_index).to(self.device)
                x_mask = torch.Tensor(x_mask).to(self.device)

                with torch.no_grad():
                    _, mol_prediction = self.model(
                        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                    )

                    y_pred = mol_prediction.squeeze().cpu().numpy()

                    for i, orig_idx in enumerate(batch_indices):
                        predicted_value = float(y_pred[i])
                        result_df.loc[orig_idx, 'predicted_value'] = predicted_value

        except Exception as e:
            print(f"❌ 批量预测错误: {str(e)}")
            for idx in valid_indices:
                result_df.loc[idx, 'error'] = f"预测错误: {str(e)}"

        print("✅ 批量预测完成")
        return result_df


def predict_on_input_file(input_file_path, model_path=None, output_file_path=None):
    """
    对输入文件进行回归预测，并将预测值保存在原输入文件中

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径，如果为None则使用默认模型
        output_file_path: 输出文件路径，如果为None则在原文件名后加_predicted
    """
    print("🔮 开始对输入文件进行预测...")
    print("=" * 60)

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"❌ 输入文件不存在: {input_file_path}")
        return

    # 2. 确定模型路径
    if model_path is None:
        model_path = 'save_model/global_best_model.pt'
        if not os.path.exists(model_path):
            model_path = 'save_model/best_model_fold_0.pt'

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return

    # 3. 确定输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_predicted.xlsx"

    print(f"📂 输入文件: {input_file_path}")
    print(f"🤖 模型文件: {model_path}")
    print(f"💾 输出文件: {output_file_path}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")

    # 4. 加载模型
    try:
        predictor = RegressionPredictor(model_path)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return

    # 5. 加载输入数据
    try:
        df = pd.read_excel(input_file_path)
        print(f"📊 加载数据成功，共 {len(df)} 个样本")
        print(f"📋 数据列: {list(df.columns)}")

        # 检查必要列
        required_columns = ['smiles']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ 输入数据缺少必要列: {missing_columns}")
            return

    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return

    # 6. 进行批量预测
    print("🔄 开始预测...")
    try:
        result_df = predictor.predict_batch(df, batch_size=BATCH_SIZE)
        print("✅ 预测完成")

    except Exception as e:
        print(f"❌ 预测过程出错: {str(e)}")
        return

    # 7. 保存结果
    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"✅ 结果已保存到: {output_file_path}")

        # 显示统计结果
        valid_predictions = result_df[result_df['error'] == '']
        if len(valid_predictions) > 0:
            successful_predictions = result_df[~result_df['predicted_value'].isna()]
            avg_prediction = successful_predictions['predicted_value'].mean()
            min_prediction = successful_predictions['predicted_value'].min()
            max_prediction = successful_predictions['predicted_value'].max()

            print(f"\n📊 预测统计:")
            print(f"   成功预测: {len(successful_predictions)}/{len(result_df)}")
            print(f"   预测值范围: {min_prediction:.4f} - {max_prediction:.4f}")
            print(f"   平均预测值: {avg_prediction:.4f}")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"   预测失败: {len(errors)}")

    except Exception as e:
        print(f"❌ 保存结果失败: {str(e)}")


def run_single_prediction():
    """运行单个SMILES预测"""
    print("=" * 60)
    print("🧪 单个SMILES回归预测")
    print("=" * 60)

    predictor = RegressionPredictor(MODEL_PATH)

    print(f"SMILES: {SINGLE_SMILES}")

    result = predictor.predict_single(SINGLE_SMILES)

    print(f"\n📊 预测结果:")
    if result.get('error'):
        print(f"❌ 错误: {result['error']}")
    else:
        print(f"   原始SMILES: {result['smiles']}")
        print(f"   标准SMILES: {result['canonical_smiles']}")
        print(f"   预测值: {result['predicted_value']:.4f}")


def run_file_prediction(mode):
    """运行文件预测（CSV或Excel）"""
    print("=" * 60)
    print(f"📁 {mode.upper()}文件回归预测")
    print("=" * 60)

    predictor = RegressionPredictor(MODEL_PATH)

    # 根据模式选择文件
    if mode == 'csv':
        input_file, output_file = CSV_INPUT_FILE, CSV_OUTPUT_FILE
        load_func, save_func = pd.read_csv, lambda df, path: df.to_csv(path, index=False, encoding='utf-8-sig')
    else:  # excel
        input_file, output_file = EXCEL_INPUT_FILE, EXCEL_OUTPUT_FILE
        load_func, save_func = pd.read_excel, lambda df, path: df.to_excel(path, index=False)

    # 加载数据
    if os.path.exists(input_file):
        df = load_func(input_file)
        print(f"📂 从文件加载数据: {input_file}")
        print(f"   样本数量: {len(df)}")
        print(f"   列名: {list(df.columns)}")

        # 预测
        result_df = predictor.predict_batch(df, batch_size=BATCH_SIZE)

        # 保存结果
        save_func(result_df, output_file)
        print(f"✅ 结果已保存到: {output_file}")

        # 显示统计结果
        show_prediction_stats(result_df)
    else:
        print(f"❌ 输入文件不存在: {input_file}")


def show_prediction_stats(result_df):
    """显示预测统计结果"""
    successful_predictions = result_df[~result_df['predicted_value'].isna()]
    if len(successful_predictions) > 0:
        avg_prediction = successful_predictions['predicted_value'].mean()
        min_prediction = successful_predictions['predicted_value'].min()
        max_prediction = successful_predictions['predicted_value'].max()
        std_prediction = successful_predictions['predicted_value'].std()

        print(f"\n📊 预测统计:")
        print(f"   成功预测: {len(successful_predictions)}/{len(result_df)}")
        print(f"   预测值范围: {min_prediction:.4f} - {max_prediction:.4f}")
        print(f"   平均预测值: {avg_prediction:.4f}")
        print(f"   标准差: {std_prediction:.4f}")

        # 显示详细结果
        print(f"\n📋 详细结果:")
        for _, row in result_df.iterrows():
            if row['error']:
                print(f"   {row.get('compound_name', row['smiles'][:10])}: ❌ {row['error']}")
            else:
                print(f"   {row.get('compound_name', row['smiles'][:10])}: {row['predicted_value']:.4f}")

    error_count = (result_df['error'] != '').sum()
    if error_count > 0:
        print(f"   ⚠️ 错误样本: {error_count}")


def main():
    """主函数"""
    print("🐟 鱼类急性毒性回归预测系统")
    print("=" * 60)

    # 检查模型是否存在
    if not os.path.exists(MODEL_PATH):
        print(f"❌ 模型文件不存在: {MODEL_PATH}")
        print("请先运行训练脚本 fishat_training.py 来训练模型")
        return

    # 检查预测模式
    if PREDICTION_MODE not in ['single', 'csv', 'excel']:
        print(f"❌ 无效的预测模式: {PREDICTION_MODE}")
        print("支持的模式: 'single', 'csv', 'excel'")
        return

    print(f"📋 当前预测模式: {PREDICTION_MODE}")

    try:
        # 根据模式运行相应的预测
        if PREDICTION_MODE == 'single':
            run_single_prediction()
        elif PREDICTION_MODE in ['csv', 'excel']:
            run_file_prediction(PREDICTION_MODE)

        print("\n" + "=" * 60)
        print("✅ 预测完成！")
        print("=" * 60)

    except Exception as e:
        print(f"❌ 程序执行错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
