import torch
import torch.nn as nn
import torch.nn.functional as F
from AttentiveFP import Fingerprint
import pandas as pd
import numpy as np

class RegressionFingerprint(nn.Module):
    """
    基于AttentiveFP的回归模型，用于预测连续数值
    """
    def __init__(self, radius, T, input_feature_dim, input_bond_dim,
                 fingerprint_dim, output_units_num=1, p_dropout=0.1):
        super(RegressionFingerprint, self).__init__()

        # AttentiveFP模块处理SMILES
        self.fingerprint_model = Fingerprint(
            radius=radius,
            T=T,
            input_feature_dim=input_feature_dim,
            input_bond_dim=input_bond_dim,
            fingerprint_dim=fingerprint_dim,
            output_units_num=fingerprint_dim,  # 输出指纹维度而不是最终预测
            p_dropout=p_dropout
        )

        # 回归输出层
        self.regression_layer = nn.Sequential(
            nn.Linear(fingerprint_dim, fingerprint_dim // 2),
            nn.ReLU(),
            nn.Dropout(p_dropout),
            nn.Linear(fingerprint_dim // 2, fingerprint_dim // 4),
            nn.ReLU(),
            nn.Dropout(p_dropout),
            nn.Linear(fingerprint_dim // 4, output_units_num)
        )

    def forward(self, x_atom, x_bonds, x_atom_index, x_bond_index, x_mask):
        # 通过AttentiveFP获取分子指纹
        atoms_prediction, mol_fingerprint = self.fingerprint_model(
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
        )

        # 回归预测
        output = self.regression_layer(mol_fingerprint)

        return atoms_prediction, output

def prepare_smiles_data(smiles_list, feature_dicts):
    """
    准备SMILES数据用于回归模型
    """
    from AttentiveFP import get_smiles_array

    # 处理SMILES数据
    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, smiles_to_rdkit_list = get_smiles_array(
        smiles_list, feature_dicts
    )

    return (x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, smiles_to_rdkit_list)

def train_regression(model, dataset, optimizer, loss_function, feature_dicts,
                    target_column, batch_size, epoch):
    """
    回归模型训练函数
    """
    model.train()
    np.random.seed(epoch)
    valList = np.arange(0, dataset.shape[0])
    np.random.shuffle(valList)

    batch_list = []
    for i in range(0, dataset.shape[0], batch_size):
        batch = valList[i:i+batch_size]
        batch_list.append(batch)

    for counter, train_batch in enumerate(batch_list):
        batch_df = dataset.loc[train_batch, :].reset_index(drop=True)
        smiles_list = batch_df.smiles.values

        # 准备SMILES数据
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
            smiles_list, feature_dicts
        )

        # 转换为tensor
        x_atom = torch.Tensor(x_atom)
        x_bonds = torch.Tensor(x_bonds)
        x_atom_index = torch.cuda.LongTensor(x_atom_index) if torch.cuda.is_available() else torch.LongTensor(x_atom_index)
        x_bond_index = torch.cuda.LongTensor(x_bond_index) if torch.cuda.is_available() else torch.LongTensor(x_bond_index)
        x_mask = torch.Tensor(x_mask)

        # 前向传播
        atoms_prediction, mol_prediction = model(
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
        )

        optimizer.zero_grad()

        # 获取目标值
        y_val = batch_df[target_column].values

        # 过滤有效值（非NaN）
        validInds = np.where(~np.isnan(y_val))[0]
        if len(validInds) == 0:
            continue

        y_val_adjust = np.array([y_val[v] for v in validInds]).astype(float)
        validInds = torch.cuda.LongTensor(validInds).squeeze() if torch.cuda.is_available() else torch.LongTensor(validInds).squeeze()
        y_pred_adjust = torch.index_select(mol_prediction.squeeze(), 0, validInds)

        y_val_tensor = torch.cuda.FloatTensor(y_val_adjust) if torch.cuda.is_available() else torch.FloatTensor(y_val_adjust)
        loss = loss_function(y_pred_adjust, y_val_tensor)

        loss.backward()
        optimizer.step()

def eval_regression(model, dataset, loss_function, feature_dicts, target_column, batch_size):
    """
    回归模型评估函数
    """
    model.eval()
    y_val_list = []
    y_pred_list = []
    losses_list = []
    valList = np.arange(0, dataset.shape[0])

    batch_list = []
    for i in range(0, dataset.shape[0], batch_size):
        batch = valList[i:i+batch_size]
        batch_list.append(batch)

    for counter, eval_batch in enumerate(batch_list):
        batch_df = dataset.loc[eval_batch, :].reset_index(drop=True)
        smiles_list = batch_df.smiles.values

        # 准备SMILES数据
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
            smiles_list, feature_dicts
        )

        # 转换为tensor
        x_atom = torch.Tensor(x_atom)
        x_bonds = torch.Tensor(x_bonds)
        x_atom_index = torch.cuda.LongTensor(x_atom_index) if torch.cuda.is_available() else torch.LongTensor(x_atom_index)
        x_bond_index = torch.cuda.LongTensor(x_bond_index) if torch.cuda.is_available() else torch.LongTensor(x_bond_index)
        x_mask = torch.Tensor(x_mask)

        with torch.no_grad():
            # 前向传播
            atoms_prediction, mol_prediction = model(
                x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
            )

            # 获取目标值
            y_val = batch_df[target_column].values

            # 过滤有效值（非NaN）
            validInds = np.where(~np.isnan(y_val))[0]
            if len(validInds) == 0:
                continue

            y_val_adjust = np.array([y_val[v] for v in validInds]).astype(float)
            validInds = torch.cuda.LongTensor(validInds).squeeze() if torch.cuda.is_available() else torch.LongTensor(validInds).squeeze()
            y_pred_adjust = torch.index_select(mol_prediction.squeeze(), 0, validInds)

            y_val_tensor = torch.cuda.FloatTensor(y_val_adjust) if torch.cuda.is_available() else torch.FloatTensor(y_val_adjust)
            loss = loss_function(y_pred_adjust, y_val_tensor)

            losses_list.append(loss.cpu().detach().numpy())
            y_val_list.extend(y_val_adjust)
            y_pred_list.extend(y_pred_adjust.cpu().numpy())

    from sklearn.metrics import mean_squared_error, r2_score
    eval_mse = mean_squared_error(y_val_list, y_pred_list)
    eval_r2 = r2_score(y_val_list, y_pred_list)
    eval_loss = np.array(losses_list).mean()

    return eval_mse, eval_r2, eval_loss
