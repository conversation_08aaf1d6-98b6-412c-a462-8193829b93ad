=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: CMorgan
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 2310.18 seconds

Best Parameters:
  alpha: 0.1
  colsample_bytree: 0.8
  gamma: 0
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.3279
   RMSE: 0.5727
   MAE: 0.4376
   R2: 0.8143
   MAPE: 1.3097

2. 10-Fold Cross Validation Results:
   Mean R²: 0.3680 ± 0.1322
   Best CV Score: 0.3680
   Individual fold R² scores: ['0.5106', '0.6468', '0.4549', '0.3513', '0.3594', '0.3122', '0.2340', '0.1577', '0.3241', '0.3287']

3. External Validation Set Metrics:
   MSE: 0.8585
   RMSE: 0.9266
   MAE: 0.7109
   R2: 0.5222
   MAPE: 2.4450

Feature Information:
  Numerical Features: []
  Categorical Features: []

