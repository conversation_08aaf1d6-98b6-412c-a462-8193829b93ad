# ChemBERTa预训练和微调项目

使用ChemBERTa模型进行化学分子的自监督预训练和pDMImEC50回归任务微调。

## 项目结构

```
chembert_project/
├── src/                   # 核心代码
│   ├── data_processor.py  # 数据处理
│   ├── pretrain.py        # 预训练模块
│   └── finetune.py        # 微调模块
├── configs/config.py      # 基础配置
├── data/                  # 数据文件
│   ├── Zinc.csv          # 预训练数据（33万+分子）
│   └── DMAT.csv          # 微调数据（3653个样本）
├── models/               # 模型保存目录
├── run.py               # 主程序（一键运行）
├── requirements.txt     # 依赖包
└── README.md           # 本文档
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置参数
打开 `run.py` 文件，修改配置区域：

```python
# 运行模式
RUN_MODE = "both"  # "both": 完整流程, "pretrain": 仅预训练, "finetune": 仅微调

# 训练参数
EPOCHS_PRETRAIN = 2      # 预训练轮数
EPOCHS_FINETUNE = 5      # 微调轮数
BATCH_SIZE_PRETRAIN = 16 # 预训练批次大小
BATCH_SIZE_FINETUNE = 16 # 微调批次大小

# 模型设置
USE_LOCAL_MODEL = True   # True: 本地模型, False: 在线下载
```

### 3. 运行训练
```bash
python run.py
```

## 配置选项

### 预设配置
在 `run.py` 中可以使用预设配置：

```python
USE_PRESET = "standard"  # 选项: "quick_test", "standard", "high_quality"
```

- **quick_test**: 快速测试（1轮预训练，3轮微调）
- **standard**: 标准训练（2轮预训练，5轮微调）
- **high_quality**: 高质量训练（3轮预训练，10轮微调）

### 本地模型部署
程序会自动下载ChemBERTa模型到本地，实现离线运行：
- 模型大小: ~300MB
- 本地路径: `models/chembert_base/`
- 首次运行会自动下载，后续离线使用

## 数据说明

- **预训练数据**: Zinc.csv（33万+化学分子SMILES，无标签预训练）
- **微调数据**: DMAT.csv（3653个样本，目标列pDMImEC50）
- **数据分割**: 训练集70% / 验证集10% / 测试集20%

## 训练流程

1. **预训练阶段**: 使用Zinc.csv进行掩码语言模型预训练
2. **微调阶段**: 使用DMAT.csv进行回归任务微调
3. **评估**: 输出R²、MAE、RMSE等指标

## 输出结果

- 预训练模型: `models/pretrained/`
- 微调模型: `models/finetuned/`
- 训练日志: 实时显示损失和验证指标
- 最终测试结果: R²、MAE、RMSE

## 技术细节

- **基础模型**: ChemBERTa-77M-MLM
- **预训练**: 掩码语言模型（MLM）
- **微调**: 回归任务（预测pDMImEC50）
- **优化器**: AdamW + 线性学习率调度
- **设备**: 自动检测GPU/CPU
