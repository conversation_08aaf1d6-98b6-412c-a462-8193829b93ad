=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: PubChem
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 1528.41 seconds

Best Parameters:
  alpha: 0
  colsample_bytree: 0.8
  gamma: 0.1
  lambda: 1
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.2593
   RMSE: 0.5092
   MAE: 0.3795
   R2: 0.8532
   MAPE: 1.4687

2. 10-Fold Cross Validation Results:
   Mean R²: 0.3775 ± 0.1399
   Best CV Score: 0.3775
   Individual fold R² scores: ['0.5287', '0.6325', '0.4383', '0.4267', '0.4243', '0.1893', '0.2276', '0.1744', '0.3654', '0.3678']

3. External Validation Set Metrics:
   MSE: 0.8788
   RMSE: 0.9375
   MAE: 0.7118
   R2: 0.5109
   MAPE: 2.0365

Feature Information:
  Numerical Features: []
  Categorical Features: []

