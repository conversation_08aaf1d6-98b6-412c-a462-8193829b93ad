=== XGBoost Regression Model Summary ===

Input File: ../data/prepro_DMImbEC50_fp.xlsx
Fingerprint: MACCS
Target Variable: pEC50_new
GPU Acceleration: Enabled
Training Time: 1035.08 seconds

Best Parameters:
  alpha: 0
  colsample_bytree: 1.0
  gamma: 0
  lambda: 2
  learning_rate: 0.1
  max_depth: 6
  n_estimators: 200
  subsample: 0.8

=== Model Performance Results ===

1. Training Set Metrics:
   MSE: 0.2274
   RMSE: 0.4769
   MAE: 0.3386
   R2: 0.8712
   MAPE: 1.2291

2. 10-Fold Cross Validation Results:
   Mean R²: 0.3605 ± 0.1415
   Best CV Score: 0.3605
   Individual fold R² scores: ['0.5551', '0.6363', '0.4169', '0.3624', '0.4098', '0.2252', '0.2153', '0.1748', '0.2882', '0.3205']

3. External Validation Set Metrics:
   MSE: 0.9674
   RMSE: 0.9836
   MAE: 0.7395
   R2: 0.4616
   MAPE: 2.9317

Feature Information:
  Numerical Features: []
  Categorical Features: []

