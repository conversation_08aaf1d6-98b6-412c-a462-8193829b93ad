import torch
import dgl
from rdkit import Chem
from rdkit.Chem import MolFromSmiles
from utils.MY_GNN import MGA  
import numpy as np

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 新增参数配置
args = {
    'select_task_list': ['pEC50'],  # 添加任务名称列表
    'atom_data_field': 'atom',
    'bond_data_field': 'etype',
    'explicit_H': False,  # 与训练时保持一致
    'use_chirality': True  # 与训练时保持一致
}

def one_of_k_atompair_encoding(x, allowable_set):
    for atompair in allowable_set:
        if x in atompair:
            x = atompair
            break
        else:
            if atompair == allowable_set[-1]:
                x = allowable_set[-1]
            else:
                continue
    return [x == s for s in allowable_set]

def one_of_k_encoding(x, allowable_set):
    if x not in allowable_set:
        raise Exception("input {0} not in allowable set{1}:".format(
            x, allowable_set))
    return [x == s for s in allowable_set]

def one_of_k_encoding_unk(x, allowable_set):
    """Maps inputs not in the allowable set to the last element."""
    if x not in allowable_set:
        x = allowable_set[-1]
    return [x == s for s in allowable_set]

def load_trained_model(model_path, device=device):
    """加载预训练模型"""
    model_args = {
        'in_feats': 40,
        'rgcn_hidden_feats': [512, 512],
        'n_tasks': 1,
        'classifier_hidden_feats': 64,
        'rgcn_drop_out': 0.2,
        'dropout': 0.2,
        'loop': True
    }
    
    model = MGA(**model_args)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device).eval()
    return model

# 添加缺失的etype_features函数
def etype_features(bond, use_chirality=True, atompair=True):
    """边类型特征计算（必须与训练时一致）"""
    bt = bond.GetBondType()
    bond_feats_1 = [
        bt == Chem.rdchem.BondType.SINGLE, 
        bt == Chem.rdchem.BondType.DOUBLE,
        bt == Chem.rdchem.BondType.TRIPLE, 
        bt == Chem.rdchem.BondType.AROMATIC,
    ]
    a = next((i for i, m in enumerate(bond_feats_1) if m), 0)

    bond_feats_2 = bond.GetIsConjugated()
    b = 1 if bond_feats_2 else 0

    bond_feats_3 = bond.IsInRing()
    c = 1 if bond_feats_3 else 0

    index = a * 1 + b * 4 + c * 8
    
    if use_chirality:
        bond_feats_4 = one_of_k_encoding_unk(
            str(bond.GetStereo()),
            ["STEREONONE", "STEREOANY", "STEREOZ", "STEREOE"])
        d = next((i for i, m in enumerate(bond_feats_4) if m), 0)
        index += d * 16
    
    if atompair:
        atom_pair_str = bond.GetBeginAtom().GetSymbol() + bond.GetEndAtom().GetSymbol()
        bond_feats_5 = one_of_k_atompair_encoding(
            atom_pair_str, 
            [['CC'], ['CN', 'NC'], ['ON', 'NO'], ['CO', 'OC'], ['CS', 'SC'],
             ['SO', 'OS'], ['NN'], ['SN', 'NS'], ['CCl', 'ClC'], ['CF', 'FC'],
             ['CBr', 'BrC'], ['others']]
        )
        e = next((i for i, m in enumerate(bond_feats_5) if m), 0)
        index += e * 64
    
    return index



def atom_features(atom):
    """原子特征（修复参数传递）"""
    return atom_features_impl(
        atom, 
        explicit_H=args['explicit_H'],
        use_chirality=args['use_chirality']
    )

def atom_features_impl(atom, explicit_H, use_chirality):
    """实际的原子特征实现"""
    # 保持原有实现不变
    results = one_of_k_encoding_unk(
        atom.GetSymbol(),
        ['B','C','N','O','F','Si','P','S','Cl','As','Se','Br','Te','I','At','other']
    ) + one_of_k_encoding(atom.GetDegree(), [0,1,2,3,4,5,6]) + \
    [atom.GetFormalCharge(), atom.GetNumRadicalElectrons()] + \
    one_of_k_encoding_unk(atom.GetHybridization(), [
        Chem.rdchem.HybridizationType.SP, 
        Chem.rdchem.HybridizationType.SP2,
        Chem.rdchem.HybridizationType.SP3, 
        Chem.rdchem.HybridizationType.SP3D,
        Chem.rdchem.HybridizationType.SP3D2,'other'
    ]) + [atom.GetIsAromatic()]
    
    if not explicit_H:
        results += one_of_k_encoding_unk(atom.GetTotalNumHs(), [0,1,2,3,4])
    
    if use_chirality:
        try:
            results += one_of_k_encoding_unk(atom.GetProp('_CIPCode'), ['R','S']) + \
                      [atom.HasProp('_ChiralityPossible')]
        except:
            results += [False, False] + [atom.HasProp('_ChiralityPossible')]
    
    return np.array(results)

def construct_molecule_graph(smiles):
    """修复图构造"""
    mol = MolFromSmiles(smiles)
    if mol is None:
        raise ValueError(f"Invalid SMILES: {smiles}")
    
    g = dgl.DGLGraph()
    num_atoms = mol.GetNumAtoms()
    g.add_nodes(num_atoms)
    
    # 原子特征
    atom_features_list = [atom_features(atom) for atom in mol.GetAtoms()]
    g.ndata[args['atom_data_field']] = torch.tensor(
        np.array(atom_features_list),
        dtype=torch.float32
    )
    
    # 边特征
    src, dst, etypes = [], [], []
    for bond in mol.GetBonds():
        u = bond.GetBeginAtomIdx()
        v = bond.GetEndAtomIdx()
        # 添加双向边
        src.extend([u, v])
        dst.extend([v, u])
        # 计算边类型
        etype = etype_features(bond)
        etypes.extend([etype, etype])
    
    g.add_edges(src, dst)
    g.edata[args['bond_data_field']] = torch.tensor(etypes, dtype=torch.long)
    return g

def predict_smiles(model, smiles_list, batch_size=128):
    """修复批量预测"""
    all_preds = []
    
    for i in range(0, len(smiles_list), batch_size):
        batch_smiles = smiles_list[i:i+batch_size]
        try:
            graphs = []
            for smi in batch_smiles:
                try:
                    graphs.append(construct_molecule_graph(smi))
                except ValueError as e:
                    print(f"Invalid SMILES: {smi}, {str(e)}")
                    graphs.append(dgl.DGLGraph())  # 添加空图占位
            
            # 过滤无效图
            valid_graphs = [g for g in graphs if g.num_nodes() > 0]
            if not valid_graphs:
                all_preds.extend([np.nan]*len(batch_smiles))
                continue
            
            bg = dgl.batch(valid_graphs).to(device)
            atom_feats = bg.ndata[args['atom_data_field']].to(device)
            bond_feats = bg.edata[args['bond_data_field']].to(device)
            
            with torch.no_grad():
                batch_pred = model(bg, atom_feats, bond_feats).cpu().numpy()
            
            # 处理无效预测
            pred_idx = 0
            final_preds = []
            for g in graphs:
                if g.num_nodes() > 0:
                    pred_value = batch_pred[pred_idx][0]  # 添加[0]解包标量
                    final_preds.append(pred_value)
                    pred_idx += 1
                else:
                    final_preds.append(np.nan)
            all_preds.extend(final_preds)
            
        except Exception as e:
            print(f"Batch error: {str(e)}")
            all_preds.extend([np.nan]*len(batch_smiles))
    
    return np.array(all_preds)

if __name__ == "__main__":
    # 初始化配置
    config = {
        "model_path": "model/DMImbEC50_early_stop.pth",
        "test_smiles": [
            "COC(=O)\C=C\C(=O)OC",
            "CCOC(=O)CBr",
            "O=C=NC1=CC=CC2=C(C=CC=C12)N=C=O",
            "CCOC(=O)C1=C(C=CC=C1)C(=O)OCC |c:7,9,t:5,lp:2:2,4:2,12:2,13:2,Sg:n:1:5-9:ht,Sg:n:14:5-9:ht|",
            "C=CC(=O)N1CCOCC1",
            "BrC1=C(Br)C(Br)=C(CCC2=C(Br)C(Br)=C(Br)C(Br)=C2Br)C(Br)=C1Br",
            "CCCCCCCC(=O)SCCC[Si](OCC)(OCC)OCC",
            "CNC1=CC=C(C=C1)C(O)(C1=CC=C(C=C1)N(C)C)C1=CC=C(C=C1)N(C)C"        

        ]
    }
    
    # 执行预测
    print("Loading model...")
    model = load_trained_model(config["model_path"])
    
    print("Predicting...")
    predictions = predict_smiles(model, config["test_smiles"], batch_size=128)
    
    # 格式化输出
    print("\nResults:")
    for smi, pred in zip(config["test_smiles"], predictions):
        if np.isnan(pred).any():
            print(f"{smi}: Prediction failed")
        else:
            # 假设单任务输出
            print(f"{smi}: {pred:.4f}")  
            # 多任务版本：
            # for task, value in zip(args['select_task_list'], pred):
            #     print(f"  {task}: {value:.4f}")
