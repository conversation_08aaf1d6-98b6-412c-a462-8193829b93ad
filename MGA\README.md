#  Multi-task Graph Attention (MGA) framework
Multi-task Graph Attention (MGA) is a novel framework to make fully use of available toxicity data, which can simultaneously learn the regression and classification tasks for toxicity prediction. Here is the overview of the Multi-task Graph Attention framework.

![Image text](https://github.com/wzxxxx/MGA/blob/main/MGA.png)


**requirements：**  
python 3.6  
anaconda  
dgl 0.4.3  
xgboost  
rdkit  
pytorch  
sklearn  
