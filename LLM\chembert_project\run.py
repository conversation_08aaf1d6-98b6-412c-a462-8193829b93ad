#!/usr/bin/env python3
"""
ChemBERTa训练主程序 - 一键运行
直接运行: python run.py
"""

# ==================== 配置说明 ====================
# 所有配置参数都在 configs/config.py 文件中设置
# 如需修改参数，请编辑 configs/config.py 文件
# ==================== 配置说明结束 ====================

import os
import sys
import torch
from transformers import AutoModel, AutoTokenizer

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from configs.config import Config
from src.data_processor import DataProcessor
from src.pretrain import MLMPretrainer
from src.finetune import FineTuner

def download_chembert_model():
    """下载ChemBERTa模型到本地"""
    model_name = "DeepChem/ChemBERTa-77M-MLM"
    # 确保模型保存在当前项目目录下
    script_dir = os.path.dirname(os.path.abspath(__file__))
    local_model_dir = os.path.join(script_dir, "models", "chembert_base")

    print(f"下载模型: {model_name}")
    print("这可能需要几分钟时间，请耐心等待...")
    os.makedirs(local_model_dir, exist_ok=True)

    try:
        # 检查依赖
        try:
            from transformers import AutoConfig
        except ImportError:
            print("错误: 缺少transformers库")
            print("请运行: pip install transformers")
            return False

        print("步骤1/3: 下载配置文件...")
        config = AutoConfig.from_pretrained(model_name)
        config.save_pretrained(local_model_dir)

        print("步骤2/3: 下载分词器...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(local_model_dir)

        print("步骤3/3: 下载模型权重...")
        model = AutoModel.from_pretrained(model_name)
        model.save_pretrained(local_model_dir)

        print("模型下载完成")
        print(f"保存位置: {os.path.abspath(local_model_dir)}")
        return True
    except Exception as e:
        print(f"下载失败: {e}")
        print("请检查网络连接或运行独立下载脚本: python download_model.py")
        return False

def update_config_for_local_model():
    """更新配置使用本地模型"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, "configs", "config.py")

    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换模型路径
        old_line = 'MODEL_NAME = "DeepChem/ChemBERTa-77M-MLM"'
        new_line = 'MODEL_NAME = "models/chembert_base"'

        if old_line in content:
            content = content.replace(old_line, new_line)

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)

            print("配置已更新为本地模型")
        else:
            print("配置已是本地模式")

    except Exception as e:
        print(f"配置更新失败: {e}")

def setup_model(config):
    """设置模型（下载或配置本地模型）"""
    if config.use_local_model:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        local_model_dir = os.path.join(script_dir, "models", "chembert_base")
        required_files = ["config.json", "tokenizer.json"]
        # 检查模型权重文件（可能是 pytorch_model.bin 或 model.safetensors）
        model_weight_files = ["pytorch_model.bin", "model.safetensors"]

        # 检查本地模型是否完整
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(local_model_dir, file)):
                missing_files.append(file)

        # 检查模型权重文件
        has_model_weights = any(
            os.path.exists(os.path.join(local_model_dir, weight_file))
            for weight_file in model_weight_files
        )
        if not has_model_weights:
            missing_files.append("模型权重文件")

        if missing_files:
            print(f"本地模型不完整，缺少文件: {missing_files}")
            print("开始下载模型...")
            success = download_chembert_model()
            if success:
                update_config_for_local_model()
                print("本地模型设置完成")
            else:
                print("模型下载失败，将使用在线模式")
                return False
        else:
            print("使用本地模型")
            update_config_for_local_model()
    else:
        print("使用在线模型")

    return True

def main():
    print("ChemBERTa训练程序")
    print("=" * 50)

    # 初始化配置
    config = Config()

    # 显示当前配置
    print("运行配置:")
    print(f"  运行模式: {config.run_mode}")
    print(f"  使用本地模型: {config.use_local_model}")
    print(f"  最大序列长度: {config.max_length}")
    print(f"  跳过预训练: {config.skip_pretrain}")

    if config.run_mode in ['pretrain', 'both']:
        print("预训练配置:")
        print(f"  轮数: {config.pretrain_epochs}")
        print(f"  批次大小: {config.pretrain_batch_size}")
        print(f"  学习率: {config.pretrain_lr}")

    if config.run_mode in ['finetune', 'both']:
        print("微调配置:")
        print(f"  轮数: {config.finetune_epochs}")
        print(f"  批次大小: {config.finetune_batch_size}")
        print(f"  学习率: {config.finetune_lr}")
        print(f"  目标列: {config.target_column}")

    # 设置模型
    model_setup_success = setup_model(config)

    # 根据模型设置结果决定使用本地还是在线模型
    if config.use_local_model and model_setup_success:
        # 配置文件已经设置了正确的绝对路径，不需要再次设置
        print("使用本地模型")
    else:
        config.model_name = "DeepChem/ChemBERTa-77M-MLM"
        print("使用在线模型")

    # GPU检查
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        config.device = "cuda"
    else:
        print("使用CPU")
        config.device = "cpu"
    
    print("=" * 50)
    
    # 数据处理
    data_processor = DataProcessor(config)
    
    # 预训练
    if config.run_mode in ['pretrain', 'both'] and not config.skip_pretrain:
        print("开始预训练...")
        pretrain_loader = data_processor.create_pretrain_dataloader()
        pretrainer = MLMPretrainer(config)
        pretrainer.train(pretrain_loader)
        print("预训练完成!")

    # 微调
    if config.run_mode in ['finetune', 'both']:
        print("开始微调...")
        train_loader, val_loader, test_loader = data_processor.create_finetune_dataloaders()
        finetuner = FineTuner(config)
        finetuner.train(train_loader, val_loader)

        print("测试结果:")
        test_metrics = finetuner.test(test_loader)
        print(f"  R² Score: {test_metrics['r2']:.4f}")
        print(f"  MAE: {test_metrics['mae']:.4f}")
        print(f"  RMSE: {test_metrics['rmse']:.4f}")
        print("微调完成!")

    print("全部完成!")

if __name__ == "__main__":
    main()
