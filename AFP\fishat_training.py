#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
鱼类急性毒性回归预测模型训练脚本
基于AttentiveFP处理分子结构进行回归预测
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import pandas as pd
import pickle
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# 导入AttentiveFP相关模块
from AttentiveFP import save_smiles_dicts, get_smiles_array
from RegressionFP import RegressionFingerprint, train_regression, eval_regression

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    torch.manual_seed(8)
    np.random.seed(8)

    if torch.cuda.is_available():
        torch.set_default_tensor_type('torch.cuda.FloatTensor')
        torch.backends.cudnn.benchmark = True
        print("✅ 使用GPU进行训练")
    else:
        print("✅ 使用CPU进行训练")

def load_and_preprocess_data():
    """加载和预处理数据"""
    print("=== 数据加载和预处理 ===")

    # 数据路径
    raw_filename = "../../data/prepro_DMImbEC50.xlsx"
    print(f"📂 数据文件路径: {raw_filename}")
    feature_filename = raw_filename.replace('.xlsx', '.pickle')
    filename = raw_filename.replace('.xlsx', '')
    prefix_filename = raw_filename.split('/')[-1].replace('.xlsx', '')

    # 加载数据
    smiles_tasks_df = pd.read_excel(raw_filename)
    # 处理SMILES数据
    from rdkit import Chem
    smilesList = smiles_tasks_df.smiles.values


    atom_num_dist = []
    remained_smiles = []
    canonical_smiles_list = []
    valid_indices = []

    for i, smiles in enumerate(smilesList):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                atom_num_dist.append(len(mol.GetAtoms()))
                remained_smiles.append(smiles)
                canonical_smiles_list.append(Chem.MolToSmiles(mol, isomericSmiles=True))
                valid_indices.append(i)
        except:
            print(f"无法处理的SMILES: {smiles}")
            continue

    print(f"成功处理的SMILES数量: {len(remained_smiles)}")

    # 过滤数据
    smiles_tasks_df = smiles_tasks_df.iloc[valid_indices].reset_index(drop=True)
    smiles_tasks_df['cano_smiles'] = canonical_smiles_list

    print(f"过滤后数据形状: {smiles_tasks_df.shape}")
    print(f"原子数统计: 最小={min(atom_num_dist)}, 最大={max(atom_num_dist)}, 平均={np.mean(atom_num_dist):.2f}")

    # 显示数据列名
    print(f"数据列名: {list(smiles_tasks_df.columns)}")

    return smiles_tasks_df, canonical_smiles_list, feature_filename, filename, prefix_filename

def process_features(smiles_tasks_df, canonical_smiles_list, feature_filename, filename):
    """处理SMILES特征"""
    print("=== SMILES特征处理 ===")

    # 处理SMILES特征
    if os.path.isfile(feature_filename):
        feature_dicts = pickle.load(open(feature_filename, "rb"))
        print("从缓存加载SMILES特征")
    else:
        print("生成SMILES特征...")
        feature_dicts = save_smiles_dicts(canonical_smiles_list, filename)
        print("SMILES特征生成完成")

    # 过滤可处理的数据
    remained_df = smiles_tasks_df[smiles_tasks_df["cano_smiles"].isin(feature_dicts['smiles_to_atom_mask'].keys())]
    uncovered_df = smiles_tasks_df.drop(remained_df.index)

    print(f"可处理的数据: {remained_df.shape[0]}")
    print(f"无法处理的数据: {uncovered_df.shape[0]}")

    remained_df = remained_df.reset_index(drop=True)

    # 重要：确保使用canonical SMILES进行训练
    remained_df['smiles'] = remained_df['cano_smiles']

    return remained_df, feature_dicts

def split_data(remained_df, fold_idx=0):
    """数据分割 - 支持十次随机划分"""

    # 使用不同的随机种子进行十次划分
    random_seed = 42 + fold_idx * 10

    # 数据分割
    test_df = remained_df.sample(frac=1/10, random_state=random_seed)
    training_data = remained_df.drop(test_df.index)
    valid_df = training_data.sample(frac=1/9, random_state=random_seed)
    train_df = training_data.drop(valid_df.index)

    train_df = train_df.reset_index(drop=True)
    valid_df = valid_df.reset_index(drop=True)
    test_df = test_df.reset_index(drop=True)

    return train_df, valid_df, test_df

def build_model(canonical_smiles_list, feature_dicts):
    """构建回归模型"""
    print("=== 回归模型构建 ===")

    # 模型参数
    target_column = 'pEC50_new'  # 假设目标列为LC50，需要根据实际数据调整
    batch_size = 256
    epochs = 200
    p_dropout = 0.2
    fingerprint_dim = 150
    radius = 3
    T = 2
    weight_decay = 5
    learning_rate = 3
    output_units_num = 1  # 回归任务输出1个数值

    # 获取特征维度
    x_atom, x_bonds, _, _, _, _ = get_smiles_array([canonical_smiles_list[0]], feature_dicts)
    input_feature_dim = x_atom.shape[-1]
    input_bond_dim = x_bonds.shape[-1]

    print(f"原子特征维度: {input_feature_dim}")
    print(f"键特征维度: {input_bond_dim}")

    # 构建回归模型
    model = RegressionFingerprint(
        radius=radius,
        T=T,
        input_feature_dim=input_feature_dim,
        input_bond_dim=input_bond_dim,
        fingerprint_dim=fingerprint_dim,
        output_units_num=output_units_num,
        p_dropout=p_dropout
    )

    if torch.cuda.is_available():
        model.cuda()

    return model, target_column, batch_size, epochs, weight_decay, learning_rate

def get_regression_metrics(model, df, feature_dicts, target_column, batch_size):
    """计算回归指标"""
    model.eval()
    predictions = []
    labels = []

    valList = np.arange(0, df.shape[0])
    batch_list = [valList[i:i+batch_size] for i in range(0, len(valList), batch_size)]

    for batch in batch_list:
        batch_df = df.loc[batch, :].reset_index(drop=True)
        smiles_list = batch_df.smiles.values

        from RegressionFP import prepare_smiles_data
        x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
            smiles_list, feature_dicts
        )

        x_atom = torch.Tensor(x_atom)
        x_bonds = torch.Tensor(x_bonds)
        x_atom_index = torch.cuda.LongTensor(x_atom_index) if torch.cuda.is_available() else torch.LongTensor(x_atom_index)
        x_bond_index = torch.cuda.LongTensor(x_bond_index) if torch.cuda.is_available() else torch.LongTensor(x_bond_index)
        x_mask = torch.Tensor(x_mask)

        with torch.no_grad():
            _, mol_prediction = model(
                x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
            )

        y_pred = mol_prediction.squeeze().cpu().numpy()
        y_true = batch_df[target_column].values

        # 过滤有效值
        valid_mask = ~np.isnan(y_true)
        if valid_mask.sum() > 0:
            predictions.extend(y_pred[valid_mask])
            labels.extend(y_true[valid_mask])

    if len(predictions) == 0:
        return float('inf'), 0.0, float('inf')

    mse = mean_squared_error(labels, predictions)
    r2 = r2_score(labels, predictions)
    mae = mean_absolute_error(labels, predictions)

    return mse, r2, mae

def train_model(model, train_df, valid_df, feature_dicts, target_column,
                batch_size, epochs, weight_decay, learning_rate, fold_idx=0):
    """训练回归模型"""

    # 损失函数和优化器
    loss_function = nn.MSELoss()  # 回归任务使用MSE损失
    optimizer = optim.Adam(model.parameters(), 10**-learning_rate, weight_decay=10**-weight_decay)

    # 训练循环
    best_param = {
        "best_epoch": 0,
        "best_r2": -float('inf'),
        "model_path": None
    }

    os.makedirs('save_model', exist_ok=True)

    for epoch in range(epochs):
        # 只在需要时进行评估以节省时间
        if epoch % 10 == 0 or epoch < 5:
            valid_mse, valid_r2, valid_loss = eval_regression(
                model, valid_df, loss_function, feature_dicts, target_column, batch_size
            )

            # 保存最佳模型（基于R²分数）
            if valid_r2 > best_param["best_r2"]:
                best_param["best_epoch"] = epoch
                best_param["best_r2"] = valid_r2
                # 保存完整模型和相关信息
                model_path = f'save_model/best_model_fold_{fold_idx}.pt'
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'model': model,
                    'epoch': epoch,
                    'valid_r2': valid_r2,
                    'valid_mse': valid_mse,
                    'feature_dicts': feature_dicts,
                    'target_column': target_column,
                    'model_config': {
                        'radius': 3,
                        'T': 2,
                        'fingerprint_dim': 150,
                        'p_dropout': 0.2,
                        'output_units_num': 1
                    }
                }, model_path)
                best_param["model_path"] = model_path
                print(f"💾 保存最佳模型: {model_path} (R²: {valid_r2:.4f})")

            # 计算回归指标
            test_mse, test_r2, test_mae = get_regression_metrics(model, valid_df, feature_dicts, target_column, batch_size)

            # 只打印必要信息
            print(f"Epoch {epoch}: R²={valid_r2:.4f}, MSE={valid_mse:.4f}, MAE={test_mae:.4f}")

        # 早停
        if epoch - best_param["best_epoch"] > 20:
            break

        # 训练一个epoch
        torch.manual_seed(epoch)
        train_regression(
            model, train_df, optimizer, loss_function, feature_dicts,
            target_column, batch_size, epoch
        )

    return best_param, None

# 删除了不再需要的函数以精简代码

def train_single_fold(train_df, valid_df, test_df, feature_dicts, canonical_smiles_list, fold_idx):
    """训练单个fold"""
    print(f"\n=== Fold {fold_idx + 1}/10 ===")

    # 构建模型
    model, target_column, batch_size, epochs, weight_decay, learning_rate = build_model(
        canonical_smiles_list, feature_dicts
    )

    # 训练模型
    best_param, _ = train_model(
        model, train_df, valid_df, feature_dicts, target_column,
        batch_size, epochs, weight_decay, learning_rate, fold_idx
    )

    # 加载最佳模型并在测试集上评估
    if best_param["model_path"] and os.path.exists(best_param["model_path"]):
        checkpoint = torch.load(best_param["model_path"])
        model = checkpoint['model']
        print(f"✅ 加载最佳模型: {best_param['model_path']}")
    else:
        print("⚠️ 使用当前模型进行测试")

    # 回归损失函数
    loss_function = nn.MSELoss()

    # 测试集评估
    test_mse, test_r2, test_loss = eval_regression(
        model, test_df, loss_function, feature_dicts, target_column, batch_size
    )

    # 获取详细回归指标
    test_mse_detail, test_r2_detail, test_mae = get_regression_metrics(
        model, test_df, feature_dicts, target_column, batch_size
    )

    print(f"Fold {fold_idx + 1} - Test R²: {test_r2:.4f}, Test MSE: {test_mse:.4f}, Test MAE: {test_mae:.4f}")

    return {
        'fold': fold_idx,
        'test_r2': test_r2,
        'test_mse': test_mse,
        'test_mae': test_mae,
        'best_epoch': best_param["best_epoch"]
    }

def main():
    """主函数 - 十次随机划分训练"""
    print("鱼类急性毒性回归预测模型训练 - 十次随机划分")
    print("=" * 60)

    # 1. 设置环境
    setup_environment()

    # 2. 加载和预处理数据
    smiles_tasks_df, canonical_smiles_list, feature_filename, filename, _ = load_and_preprocess_data()

    # 3. 处理特征
    remained_df, feature_dicts = process_features(
        smiles_tasks_df, canonical_smiles_list, feature_filename, filename
    )

    print(f"=== 数据概览 ===")
    print(f"总样本数: {remained_df.shape[0]}")

    # 4. 十次随机划分训练
    all_results = []

    for fold in range(10):
        # 数据分割
        train_df, valid_df, test_df = split_data(remained_df, fold)

        print(f"Fold {fold + 1} - 训练集: {train_df.shape[0]}, 验证集: {valid_df.shape[0]}, 测试集: {test_df.shape[0]}")

        # 训练单个fold
        result = train_single_fold(train_df, valid_df, test_df, feature_dicts, canonical_smiles_list, fold)
        all_results.append(result)

    # 5. 汇总结果
    print("\n" + "=" * 60)
    print("🎉 十次随机划分训练完成！")
    print("=" * 60)

    test_r2s = [r['test_r2'] for r in all_results]
    test_mses = [r['test_mse'] for r in all_results]
    test_maes = [r['test_mae'] for r in all_results]

    print("各Fold测试结果:")
    for i, result in enumerate(all_results):
        print(f"Fold {i+1}: R²={result['test_r2']:.4f}, MSE={result['test_mse']:.4f}, MAE={result['test_mae']:.4f}, Best_Epoch={result['best_epoch']}")

    print(f"\n📊 平均测试R²: {np.mean(test_r2s):.4f} ± {np.std(test_r2s):.4f}")
    print(f"📊 平均测试MSE: {np.mean(test_mses):.4f} ± {np.std(test_mses):.4f}")
    print(f"📊 平均测试MAE: {np.mean(test_maes):.4f} ± {np.std(test_maes):.4f}")
    print(f"📊 最佳测试R²: {np.max(test_r2s):.4f}")
    print(f"📊 最低测试MSE: {np.min(test_mses):.4f}")

    # 6. 保存全局最佳模型（基于R²分数）
    best_fold_idx = np.argmax(test_r2s)
    best_model_path = f'save_model/best_model_fold_{best_fold_idx}.pt'

    if os.path.exists(best_model_path):
        # 复制最佳模型为全局最佳模型
        import shutil
        global_best_path = 'save_model/global_best_model.pt'
        shutil.copy2(best_model_path, global_best_path)

        # 保存模型元信息
        model_info = {
            'best_fold': best_fold_idx,
            'best_test_r2': test_r2s[best_fold_idx],
            'best_test_mse': test_mses[best_fold_idx],
            'best_test_mae': test_maes[best_fold_idx],
            'mean_test_r2': np.mean(test_r2s),
            'std_test_r2': np.std(test_r2s),
            'mean_test_mse': np.mean(test_mses),
            'std_test_mse': np.std(test_mses),
            'mean_test_mae': np.mean(test_maes),
            'std_test_mae': np.std(test_maes),
            'all_results': all_results
        }

        with open('save_model/model_info.pickle', 'wb') as f:
            pickle.dump(model_info, f)

        print(f"\n🎯 全局最佳模型已保存:")
        print(f"   模型文件: {global_best_path}")
        print(f"   来源: Fold {best_fold_idx + 1}")
        print(f"   测试R²: {test_r2s[best_fold_idx]:.4f}")
        print(f"   测试MSE: {test_mses[best_fold_idx]:.4f}")
        print(f"   测试MAE: {test_maes[best_fold_idx]:.4f}")
        print(f"   模型信息: save_model/model_info.pickle")

def predict_on_input_file(input_file_path, model_path=None, output_file_path=None):
    """
    对输入文件进行回归预测，并将预测值保存在原输入文件中

    Args:
        input_file_path: 输入文件路径（Excel格式）
        model_path: 模型文件路径，如果为None则使用全局最佳模型
        output_file_path: 输出文件路径，如果为None则在原文件名后加_predicted
    """
    print("🔮 开始对输入文件进行回归预测...")
    print("=" * 60)

    # 1. 检查输入文件
    if not os.path.exists(input_file_path):
        print(f"❌ 输入文件不存在: {input_file_path}")
        return

    # 2. 确定模型路径
    if model_path is None:
        model_path = 'save_model/global_best_model.pt'
        if not os.path.exists(model_path):
            # 如果全局最佳模型不存在，尝试使用第一个fold的模型
            model_path = 'save_model/best_model_fold_0.pt'

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return

    print(f"📂 使用模型: {model_path}")

    # 3. 加载模型和相关信息
    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        model = checkpoint['model']
        feature_dicts = checkpoint['feature_dicts']
        target_column = checkpoint.get('target_column', 'LC50')

        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)
        model.eval()

        print(f"✅ 模型加载成功，使用设备: {device}")

    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return

    # 4. 加载输入数据
    try:
        input_df = pd.read_excel(input_file_path)
        print(f"📊 输入数据加载成功")
        print(f"   样本数量: {len(input_df)}")
        print(f"   列名: {list(input_df.columns)}")

        # 检查必要的列
        required_columns = ['smiles']
        missing_columns = [col for col in required_columns if col not in input_df.columns]
        if missing_columns:
            print(f"❌ 输入数据缺少必要列: {missing_columns}")
            return

    except Exception as e:
        print(f"❌ 输入数据加载失败: {str(e)}")
        return

    # 5. 数据预处理
    print("\n🔧 数据预处理...")

    # 处理SMILES数据
    from rdkit import Chem
    valid_indices = []
    canonical_smiles_list = []

    for i, smiles in enumerate(input_df['smiles'].values):
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                canonical_smiles = Chem.MolToSmiles(mol, isomericSmiles=True)
                canonical_smiles_list.append(canonical_smiles)
                valid_indices.append(i)
            else:
                canonical_smiles_list.append(None)
        except:
            canonical_smiles_list.append(None)

    # 添加预测结果列到原数据框
    result_df = input_df.copy()
    result_df['canonical_smiles'] = canonical_smiles_list
    result_df['predicted_value'] = np.nan  # 回归预测值
    result_df['error'] = ''

    print(f"   有效SMILES: {len(valid_indices)}/{len(input_df)}")

    if len(valid_indices) == 0:
        print("❌ 没有有效的SMILES可以预测")
        return

    # 6. 批量预测
    print("\n🔄 开始批量预测...")
    batch_size = 256

    try:
        # 过滤有效数据
        valid_df = result_df.loc[valid_indices].copy()
        valid_df['smiles'] = [canonical_smiles_list[i] for i in valid_indices]

        # 分批处理
        batch_list = [valid_indices[i:i+batch_size] for i in range(0, len(valid_indices), batch_size)]

        for batch_idx, batch_indices in enumerate(batch_list):
            print(f"   处理批次 {batch_idx + 1}/{len(batch_list)}")

            batch_df = valid_df.loc[batch_indices].reset_index(drop=True)
            batch_smiles = batch_df['smiles'].values

            # 准备SMILES数据
            from RegressionFP import prepare_smiles_data
            x_atom, x_bonds, x_atom_index, x_bond_index, x_mask, _ = prepare_smiles_data(
                batch_smiles, feature_dicts
            )

            # 转换为tensor
            x_atom = torch.Tensor(x_atom).to(device)
            x_bonds = torch.Tensor(x_bonds).to(device)
            x_atom_index = torch.LongTensor(x_atom_index).to(device)
            x_bond_index = torch.LongTensor(x_bond_index).to(device)
            x_mask = torch.Tensor(x_mask).to(device)

            # 预测
            with torch.no_grad():
                _, mol_prediction = model(
                    x_atom, x_bonds, x_atom_index, x_bond_index, x_mask
                )

                y_pred = mol_prediction.squeeze().cpu().numpy()

                # 保存预测结果
                for i, orig_idx in enumerate(batch_indices):
                    predicted_value = float(y_pred[i])
                    result_df.loc[orig_idx, 'predicted_value'] = predicted_value

        print("✅ 预测完成")

    except Exception as e:
        print(f"❌ 预测过程出错: {str(e)}")
        # 为所有有效索引标记错误
        for idx in valid_indices:
            result_df.loc[idx, 'error'] = f"预测错误: {str(e)}"

    # 7. 保存结果
    if output_file_path is None:
        # 在原文件名后加_predicted
        base_name = os.path.splitext(input_file_path)[0]
        output_file_path = f"{base_name}_predicted.xlsx"

    try:
        result_df.to_excel(output_file_path, index=False)
        print(f"\n✅ 预测结果已保存到: {output_file_path}")

        # 显示预测统计
        print("\n📊 预测统计:")
        successful_predictions = result_df[~result_df['predicted_value'].isna()]
        if len(successful_predictions) > 0:
            avg_prediction = successful_predictions['predicted_value'].mean()
            min_prediction = successful_predictions['predicted_value'].min()
            max_prediction = successful_predictions['predicted_value'].max()
            std_prediction = successful_predictions['predicted_value'].std()

            print(f"   成功预测: {len(successful_predictions)}/{len(result_df)}")
            print(f"   预测值范围: {min_prediction:.4f} - {max_prediction:.4f}")
            print(f"   平均预测值: {avg_prediction:.4f}")
            print(f"   标准差: {std_prediction:.4f}")

        errors = result_df[result_df['error'] != '']
        if len(errors) > 0:
            print(f"   预测失败: {len(errors)}")

    except Exception as e:
        print(f"❌ 保存结果失败: {str(e)}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 命令行模式：python multimodal_fishat_training.py predict input_file.xlsx [model_path] [output_file.xlsx]
        if sys.argv[1] == 'predict':
            if len(sys.argv) < 3:
                print("用法: python fishat_training.py predict input_file.xlsx [model_path] [output_file.xlsx]")
                sys.exit(1)

            input_file = sys.argv[2]
            model_path = sys.argv[3] if len(sys.argv) > 3 else None
            output_file = sys.argv[4] if len(sys.argv) > 4 else None

            predict_on_input_file(input_file, model_path, output_file)
        else:
            print("未知命令，支持的命令: predict")
    else:
        # 默认训练模式
        main()
