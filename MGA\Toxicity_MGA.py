import numpy as np
from utils import build_dataset
import torch
from torch.optim import <PERSON>
from torch.utils.data import DataLoader
from utils.MY_GNN import collate_molgraphs, EarlyStopping, run_a_train_epoch_heterogeneous, \
    run_an_eval_epoch_heterogeneous, set_random_seed, MGA, pos_weight, class_weight, run_an_eval_epoch_pih, \
    run_an_eval_epoch_heterogeneous_rmse, run_an_eval_epoch_heterogeneous_mae
from utils.MY_GNN import collate_predict, run_predict, load_pretrained
import os
import time
import pandas as pd
start = time.time()


# fix parameters of model
args = {}
args['device'] = "cuda" if torch.cuda.is_available() else "cpu"
args['atom_data_field'] = 'atom'
args['bond_data_field'] = 'etype'
# 分类指标
args['classification_AROC'] = 'roc_auc'
# 回归指标
args['regression_r2'] = 'r2'
args['regression_mse'] = 'mse'
args['regression_mae'] = 'mae'
args['regression_rmse'] = 'rmse'
# model parameter
args['num_epochs'] = 500
args['patience'] = 50
args['batch_size'] = 256
args['mode'] = 'higher'
args['in_feats'] = 40
args['rgcn_hidden_feats'] = [128, 128]
args['classifier_hidden_feats'] = 64
args['rgcn_drop_out'] = 0.3
args['drop_out'] = 0.4
args['lr'] = 3
args['weight_decay'] = 5
args['loop'] = True

# task name (model name)
args['task_name'] = 'DMAT-12812864'  # change
args['data_name'] = 'DMAT'  # change
args['times'] = 10

# selected task, generate select task index, task class, and classification_num
args['select_task_list'] = ['pDMImEC50']  # change (excel list name) - removed pAlaGroErC50 as it's not in binary file
args['select_task_index'] = []
args['classification_num'] = 0
args['regression_num'] = 0
args['all_task_list'] = ['logKow', 'pDMImEC50']  # change (excel list name) - matches binary file structure
# generate select task index
for index, task in enumerate(args['all_task_list']):
    if task in args['select_task_list']:
        args['select_task_index'].append(index)

# generate classification_num
for task in args['select_task_list']:
    if task in ['AF', '']:
        args['classification_num'] = args['classification_num'] + 1
    if task in ['logKow', 'pDMImEC50', 'pFishEL_NOEC', 'pDMRepNOEC', 'pDMImbEC50', 'pAlaGroErC50']:
        args['regression_num'] = args['regression_num'] + 1

# generate classification_num
if args['classification_num'] != 0 and args['regression_num'] != 0:
    args['task_class'] = 'classification_regression'
if args['classification_num'] != 0 and args['regression_num'] == 0:
    args['task_class'] = 'classification'
if args['classification_num'] == 0 and args['regression_num'] != 0:
    args['task_class'] = 'regression'
print('Classification task:{}, Regression Task:{}'.format(args['classification_num'], args['regression_num']))

args['bin_path'] = 'data/' + args['data_name'] + '.bin'
args['group_path'] = 'data/' + args['data_name'] + '_group.csv'


result_pd = pd.DataFrame(columns=args['select_task_list']+['group'] + args['select_task_list']+['group']
                         + args['select_task_list']+['group'])
all_times_train_result = []
all_times_val_result = []
all_times_test_result = []
for time_id in range(args['times']):
    set_random_seed(2020+time_id)
    one_time_train_result = []
    one_time_val_result = []
    one_time_test_result = []
    print('***************************************************************************************************')
    print('{}, {}/{} time'.format(args['task_name'], time_id+1, args['times']))
    print('***************************************************************************************************')
    train_set, val_set, test_set, task_number = build_dataset.load_graph_from_csv_bin_for_splited(
        bin_path=args['bin_path'],
        group_path=args['group_path'],
        select_task_index=args['select_task_index']
    )
    from torch.utils.data import ConcatDataset
    all_dataset = ConcatDataset([train_set, val_set, test_set])
    all_dataloader = DataLoader(dataset=all_dataset, batch_size=args['batch_size'], collate_fn=collate_molgraphs, shuffle=False)  
    print("Molecule graph generation is complete !")
    train_loader = DataLoader(dataset=train_set,
                              batch_size=args['batch_size'],
                              shuffle=True,
                              collate_fn=collate_molgraphs)

    val_loader = DataLoader(dataset=val_set,
                            batch_size=args['batch_size'],
                            shuffle=True,
                            collate_fn=collate_molgraphs)

    test_loader = DataLoader(dataset=test_set,
                             batch_size=args['batch_size'],
                             collate_fn=collate_molgraphs)
    pos_weight_np = pos_weight(train_set, classification_num=args['classification_num'])
    loss_criterion_c = torch.nn.BCEWithLogitsLoss(reduction='none', pos_weight=pos_weight_np.to(args['device']))
    loss_criterion_r = torch.nn.MSELoss(reduction='none')

#     class_weights = class_weight(train_set, args['classification_num'])
#     class_weights = class_weights / class_weights.norm(p=2, dim=1, keepdim=True)
#     loss_criterion_c_w = torch.nn.BCEWithLogitsLoss(
#     reduction='none',
#     pos_weight=class_weights[:, 1].to(args['device'])  # 取第二列作为正样本权重
# )

    model = MGA(in_feats=args['in_feats'], rgcn_hidden_feats=args['rgcn_hidden_feats'],
                n_tasks=task_number, rgcn_drop_out=args['rgcn_drop_out'],
                classifier_hidden_feats=args['classifier_hidden_feats'], dropout=args['drop_out'],
                loop=args['loop'])
    optimizer = Adam(model.parameters(), lr=10**-args['lr'], weight_decay=10**-args['weight_decay'])
    stopper = EarlyStopping(patience=args['patience'], task_name=args['task_name'], mode=args['mode'])
    model.to(args['device'])

    for epoch in range(args['num_epochs']):
        # Train
        run_a_train_epoch_heterogeneous(args, epoch, model, train_loader, loss_criterion_c, loss_criterion_r, optimizer)
        # run_a_train_epoch_heterogeneous(args, epoch, model, train_loader, loss_criterion_c, loss_criterion_r, optimizer, 
        #                                 loss_criterion_c_w = loss_criterion_c_w)

        # Validation and early stop
        validation_result = run_an_eval_epoch_heterogeneous(args, model, val_loader)
        val_score = np.mean(validation_result)
        early_stop = stopper.step(val_score, model)
        print('epoch {:d}/{:d}, validation {:.4f}, best validation {:.4f}'.format(
            epoch + 1, args['num_epochs'],
            val_score,  stopper.best_score)+' validation result:', validation_result)
        if early_stop:
            break
    stopper.load_checkpoint(model)

    train_r2 = run_an_eval_epoch_heterogeneous(args, model, train_loader)
    val_r2 = run_an_eval_epoch_heterogeneous(args, model, val_loader)
    test_r2 = run_an_eval_epoch_heterogeneous(args, model, test_loader)

    train_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, train_loader)
    val_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, val_loader)
    test_rmse = run_an_eval_epoch_heterogeneous_rmse(args, model, test_loader)

    train_mae = run_an_eval_epoch_heterogeneous_mae(args, model, train_loader)
    val_mae = run_an_eval_epoch_heterogeneous_mae(args, model, val_loader)
    test_mae = run_an_eval_epoch_heterogeneous_mae(args, model, test_loader)

    # deal result
    result = train_r2 + ['train'] + val_r2 + ['valid'] + test_r2 + ['test']
    result_pd.loc[time_id] = result
    print('********************************{}, {}_times_result*******************************'.format(args['task_name'], time_id+1))
    print("train_result:", train_r2, train_rmse, train_mae)
    print("val_result:", val_r2, val_rmse, val_mae)
    print("test_result:", test_r2, test_rmse, test_mae)
    run_an_eval_epoch_pih(args, model, all_dataloader, output_path='jieguo/'+args['task_name']+'prediction.csv')

result_pd.to_csv('result/'+args['task_name']+'_result.csv', index=None)
elapsed = (time.time() - start)
m, s = divmod(elapsed, 60)
h, m = divmod(m, 60)
print("Time used:", "{:d}:{:d}:{:d}".format(int(h), int(m), int(s)))


####################################### predict ##########################################
# args['pretrained_model'] = 'KOW预训练_early_stop.pth'
# pre_model = load_pretrained(model_path='model/大型溞生殖毒性_early_stop.pth', args=args).to(args['device'])
# pre_bin_path = 'prediction/' + args['data_name'] + '.bin'
# pre_input_csv = 'prediction/'+ args['data_name'] +'.csv'
# predict_set = build_dataset.load_graph_for_predict(bin_path=pre_bin_path, input_csv_path=pre_input_csv, 
#                                                    select_task_names=args['select_task_list'], device=args['device'])
# predict_loader = DataLoader(dataset=predict_set, batch_size=args['batch_size'], collate_fn=collate_predict)
# pre_output_path = 'prediction/' + args['task_name'] + '_predictions.csv'   
# result_df = run_predict(args=args,model=pre_model, data_loader=predict_loader, output_path=pre_output_path)










